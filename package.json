{"name": "backend", "version": "0.0.1", "description": "NestJS backend for login application", "private": true, "scripts": {"build": "nest build", "postbuild": "cp -r src/auth/templates dist/auth/templates", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.1.1", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^7.3.0", "@nestjs/typeorm": "^10.0.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-validator": "^0.14.0", "midtrans-client": "^1.4.2", "nodemailer": "^6.10.0", "passport": "^0.6.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "pinia": "^3.0.1", "redis": "^4.7.0", "reflect-metadata": "^0.1.13", "resend": "^4.5.1", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "vue": "^3.5.13"}, "overrides": {"@nestjs-modules/mailer": {"mjml": "^5.0.0-alpha.4"}}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@types/bcrypt": "^5.0.1", "@types/express": "^4.17.17", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^3.0.11", "@types/passport-local": "^1.0.37", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}