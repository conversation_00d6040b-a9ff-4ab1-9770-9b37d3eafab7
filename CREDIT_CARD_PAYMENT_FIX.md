# Credit Card Payment Fix

## Problem
Credit card payments were failing because the `secure: true` parameter was missing from the charge request payload. According to Midtrans documentation, this parameter is required for credit card transactions.

## Solution
Added two new methods to handle direct credit card charges with the required `secure: true` parameter:

### 1. MidtransService Methods

#### `chargeCreditCard()`
Uses the midtrans-client library's CoreApi to charge credit cards directly.

#### `chargeCreditCardDirect()`
Uses axios to make direct API calls to Midtrans charge endpoint, ensuring the `secure: true` parameter is properly sent.

### 2. New API Endpoints

#### `POST /payments/charge-credit-card`
Charges credit card using the midtrans-client library.

#### `POST /payments/charge-credit-card-direct`
Charges credit card using direct axios calls (alternative method).

### 3. Request Payload

Both endpoints accept the following payload:

```json
{
  "planId": "enterprise",
  "cardToken": "48111111-1114-ffb81bf2-aa8f-47a2-9d1d-245c94d020e2",
  "quantity": 1,
  "duration": 1
}
```

### 4. Key Changes

The main fix is in the charge payload structure:

**Before (missing secure parameter):**
```json
{
  "customer_details": {
    "email": "<EMAIL>",
    "full_name": "Novianto Nugroho"
  },
  "promo_details": null,
  "payment_params": {
    "card_token": "48111111-1114-ffb81bf2-aa8f-47a2-9d1d-245c94d020e2"
  },
  "payment_type": "credit_card"
}
```

**After (with secure parameter):**
```json
{
  "payment_type": "credit_card",
  "transaction_details": {
    "order_id": "test-order-123",
    "gross_amount": 100000
  },
  "credit_card": {
    "token_id": "48111111-1114-ffb81bf2-aa8f-47a2-9d1d-245c94d020e2",
    "secure": true
  },
  "customer_details": {
    "first_name": "Novianto Nugroho",
    "email": "<EMAIL>"
  }
}
```

### 5. Usage

To use the new credit card charge functionality:

1. **Get a card token** from Midtrans frontend (this part remains the same)
2. **Call the new endpoint** with the card token:

```bash
curl -X POST http://localhost:3000/payments/charge-credit-card \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "planId": "enterprise",
    "cardToken": "48111111-1114-ffb81bf2-aa8f-47a2-9d1d-245c94d020e2",
    "duration": 1
  }'
```

### 6. Response

Successful response:
```json
{
  "transaction_status": "capture",
  "transaction_id": "test-transaction-id",
  "orderId": "company-id-enterprise-timestamp-random",
  "fraud_status": "accept",
  "status_message": "Success"
}
```

### 7. Testing

Run the tests to verify the implementation:

```bash
npm test src/payments/midtrans.service.spec.ts
```

The tests verify that:
- The `secure: true` parameter is included in the charge request
- Both methods (library and direct axios) work correctly
- Error handling works for invalid amounts

### 8. Recommendation

Try the `charge-credit-card-direct` endpoint first, as it uses direct axios calls and ensures the payload is sent exactly as intended. If that works, you can also use the regular `charge-credit-card` endpoint.
