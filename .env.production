# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=Asdqwe123@Agentq
DB_DATABASE=agentq

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=Asdqwe123@Agentq
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_change_in_production

# Application
# PORT=3000
NODE_ENV=production

# Github OAuth
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=503b925e80bcb81297bc02619144b895cc8f30a3
GITHUB_CALLBACK_URL=https://backend-core-api.agentq.id/auth/github/callback

# Google OAuth
GOOGLE_CLIENT_ID=324359636617-2nlgcs45s5q6j1sicjedlnmjtklv2t13.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_CALLBACK_URL=https://backend-core-api.agentq.id/auth/google/callback

# Frontend URL
FRONTEND_URL=https://agentq.id

# AgentQ App URL
AGENTQ_APP_URL=https://app.agentq.id
AGENTQ_APP_API_URL=https://backend-app.agentq.id

# Resend Configuration
RESEND_API_KEY=re_LjAfU3GD_FWKthbMiSaHKmR1jXXZFqZJk
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=AgentQ

# SMTP Configuration
SMTP_HOST=smtp.titan.email
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=Noviantonugroho4@
SMTP_FROM=<EMAIL>

# Midtrans
MIDTRANS_CLIENT_KEY=Mid-client-yDOohRuMqY_m4QfN
MIDTRANS_SERVER_KEY=Mid-server-vZdpb9fMqgqbSoGx39CU6BDx