# Microservice Login Application

A microservice-based application with Vue.js frontend, NestJS backend, and PostgreSQL database.

## Project Structure

```
/
├── frontend/           # Vue.js frontend application
├── backend/            # NestJS backend application
└── README.md           # Project documentation
```

## Prerequisites

- Node.js (v16+)
- PostgreSQL
- npm or yarn

## Setup Instructions

### 1. Database Setup

Create a PostgreSQL database:

```sql
CREATE DATABASE login_app;
```

### 2. Backend Setup

```bash
cd backend
npm install
```

Update the `.env` file with your database credentials.

Start the backend:

```bash
npm run start:dev
```

### 3. Frontend Setup

```bash
cd frontend
npm install
```

Start the frontend:

```bash
npm run dev
```

## Running the Application

To run both frontend and backend concurrently:

```bash
npm run start
```

## Features

- User authentication (login/register)
- JWT-based authentication
- Protected routes
- Social login options (Google, GitHub)
- Responsive design