name: Build and Push to GCR

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GCR_LOCATION: gcr.io
  SERVICE: backend-agentq
  REGION: us-central1

jobs:
  build-and-push:
    name: Build and Push to GCR
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker to use gcloud as a credential helper
      run: |
        gcloud auth configure-docker

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.GCR_LOCATION }}/${{ env.PROJECT_ID }}/${{ env.SERVICE }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          DB_HOST=${{ secrets.DB_HOST }}
          DB_PORT=${{ secrets.DB_PORT }}
          DB_USERNAME=${{ secrets.DB_USERNAME }}
          DB_PASSWORD=${{ secrets.DB_PASSWORD }}
          DB_DATABASE=${{ secrets.DB_DATABASE }}
          REDIS_URL=${{ secrets.REDIS_URL }}
          REDIS_PASSWORD=${{ secrets.REDIS_PASSWORD }}
          REDIS_DB=${{ secrets.REDIS_DB }}
          JWT_SECRET=${{ secrets.JWT_SECRET }}
          NODE_ENV=production
          GITHUB_CLIENT_ID=${{ secrets.GITHUB_CLIENT_ID }}
          GITHUB_CLIENT_SECRET=${{ secrets.GITHUB_CLIENT_SECRET }}
          GITHUB_CALLBACK_URL=${{ secrets.GITHUB_CALLBACK_URL }}
          GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID }}
          GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET }}
          GOOGLE_CALLBACK_URL=${{ secrets.GOOGLE_CALLBACK_URL }}
          FRONTEND_URL=${{ secrets.FRONTEND_URL }}
          AGENTQ_APP_URL=${{ secrets.AGENTQ_APP_URL }}
          AGENTQ_APP_API_URL=${{ secrets.AGENTQ_APP_API_URL }}
          RESEND_API_KEY=${{ secrets.RESEND_API_KEY }}
          RESEND_FROM_EMAIL=${{ secrets.RESEND_FROM_EMAIL }}
          RESEND_FROM_NAME=${{ secrets.RESEND_FROM_NAME }}
          MIDTRANS_CLIENT_KEY=${{ secrets.MIDTRANS_CLIENT_KEY }}
          MIDTRANS_SERVER_KEY=${{ secrets.MIDTRANS_SERVER_KEY }}

    - name: Deploy to Cloud Run (Optional)
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Image pushed successfully to GCR!"
        echo "Image: ${{ env.GCR_LOCATION }}/${{ env.PROJECT_ID }}/${{ env.SERVICE }}:latest"
        echo "You can now deploy this image to Cloud Run or other services."
