# GitHub Actions CI/CD Setup

This repository contains a GitHub Actions workflow that automatically builds and pushes Docker images to Google Container Registry (GCR) when code is merged to the main branch.

## Workflow Overview

The workflow (`build-and-deploy.yml`) performs the following actions:

1. **Triggers**: Runs on pushes to `main` branch and pull requests to `main`
2. **Build**: Creates a Docker image using the Dockerfile in the repository
3. **Push**: Pushes the image to Google Container Registry
4. **Tagging**: Creates multiple tags including `latest`, branch name, and commit SHA

## Required Secrets

You need to configure the following secrets in your GitHub repository settings:

### Google Cloud Platform
- `GCP_PROJECT_ID`: Your Google Cloud Project ID
- `GCP_SA_KEY`: Service Account J<PERSON><PERSON> key with permissions to push to GCR

### Application Environment Variables
- `DB_HOST`: Database host
- `DB_PORT`: Database port
- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password
- `DB_DATABASE`: Database name
- `REDIS_URL`: Redis connection URL
- `REDIS_PASSWORD`: Redis password
- `REDIS_DB`: Redis database number
- `JWT_SECRET`: JWT secret key
- `GITHUB_CLIENT_ID`: GitHub OAuth client ID
- `GITHUB_CLIENT_SECRET`: GitHub OAuth client secret
- `GITHUB_CALLBACK_URL`: GitHub OAuth callback URL
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `GOOGLE_CALLBACK_URL`: Google OAuth callback URL
- `FRONTEND_URL`: Frontend application URL
- `AGENTQ_APP_URL`: AgentQ application URL
- `AGENTQ_APP_API_URL`: AgentQ API URL
- `RESEND_API_KEY`: Resend email service API key
- `RESEND_FROM_EMAIL`: Email sender address
- `RESEND_FROM_NAME`: Email sender name
- `MIDTRANS_CLIENT_KEY`: Midtrans payment client key
- `MIDTRANS_SERVER_KEY`: Midtrans payment server key

## Setup Instructions

### 1. Create Google Cloud Service Account

```bash
# Create service account
gcloud iam service-accounts create github-actions \
    --description="Service account for GitHub Actions" \
    --display-name="GitHub Actions"

# Grant necessary permissions
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

# Create and download key
gcloud iam service-accounts keys create key.json \
    --iam-account=github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

### 2. Configure GitHub Secrets

1. Go to your GitHub repository
2. Navigate to Settings → Secrets and variables → Actions
3. Add all the required secrets listed above
4. For `GCP_SA_KEY`, paste the entire contents of the `key.json` file

### 3. Enable Required APIs

```bash
# Enable Container Registry API
gcloud services enable containerregistry.googleapis.com

# Enable Cloud Build API (optional, for advanced builds)
gcloud services enable cloudbuild.googleapis.com
```

## Image Naming Convention

Images are pushed to GCR with the following naming pattern:
```
gcr.io/YOUR_PROJECT_ID/backend-agentq:TAG
```

Tags include:
- `latest` (for main branch)
- `main-COMMIT_SHA` (for main branch with commit SHA)
- `pr-NUMBER` (for pull requests)

## Deployment

After the image is pushed to GCR, you can deploy it to various services:

### Cloud Run
```bash
gcloud run deploy backend-agentq \
    --image gcr.io/YOUR_PROJECT_ID/backend-agentq:latest \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-agentq
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend-agentq
  template:
    metadata:
      labels:
        app: backend-agentq
    spec:
      containers:
      - name: backend-agentq
        image: gcr.io/YOUR_PROJECT_ID/backend-agentq:latest
        ports:
        - containerPort: 3000
```

## Troubleshooting

### Common Issues

1. **Authentication Error**: Ensure `GCP_SA_KEY` secret contains valid JSON
2. **Permission Denied**: Verify service account has `roles/storage.admin`
3. **Build Failures**: Check that all required secrets are configured
4. **Image Not Found**: Verify project ID and image name are correct

### Viewing Logs

- GitHub Actions logs: Repository → Actions tab
- GCR images: Google Cloud Console → Container Registry
- Cloud Run logs: Google Cloud Console → Cloud Run → Service → Logs

## Security Notes

- Never commit secrets to the repository
- Regularly rotate service account keys
- Use least privilege principle for service account permissions
- Monitor GCR for unauthorized images
