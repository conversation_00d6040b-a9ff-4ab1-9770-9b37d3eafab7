import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as midtransClient from 'midtrans-client';
import axios from 'axios';

@Injectable()
export class MidtransService {
  private snap: any;
  private irisApiUrl: string;
  private apiKey: string;

  constructor(private configService: ConfigService) {
    this.snap = new midtransClient.Snap({
      isProduction: configService.get('NODE_ENV') === 'production',
      serverKey: configService.get('MIDTRANS_SERVER_KEY'),
      clientKey: configService.get('MIDTRANS_CLIENT_KEY')
    });

    // Set up Iris API configuration
    this.irisApiUrl = configService.get('NODE_ENV') === 'production' 
      ? 'https://app.midtrans.com/iris/api' 
      : 'https://app.sandbox.midtrans.com/iris/api';
    
    this.apiKey = configService.get('MIDTRANS_SERVER_KEY');
  }

  async createTransaction(params: {
    orderId: string;
    amount: number;
    customerDetails: {
      firstName: string;
      email: string;
    };
    itemDetails: Array<{
      id: string;
      price: number;
      quantity: number;
      name: string;
    }>;
  }) {
    // Validate that amount is at least 0.01
    if (params.amount < 0.01) {
      throw new Error('Transaction amount must be at least 0.01');
    }
    
    // Calculate the sum of item details to ensure it matches the gross amount
    const itemDetailsSum = params.itemDetails.reduce(
      (sum, item) => sum + (item.price * item.quantity), 
      0
    );
    
    // Ensure the sum matches the gross amount
    if (Math.abs(itemDetailsSum - params.amount) > 0.01) {
      throw new Error('Transaction amount must equal the sum of item details');
    }
    
    // Ensure all items have a name
    for (const item of params.itemDetails) {
      if (!item.name || item.name.trim() === '') {
        throw new Error('All items must have a name');
      }
    }

    const parameter = {
      transaction_details: {
        order_id: params.orderId,
        gross_amount: params.amount
      },
      customer_details: {
        first_name: params.customerDetails.firstName,
        email: params.customerDetails.email
      },
      item_details: params.itemDetails,
      callbacks: {
        finish: `${this.configService.get('FRONTEND_URL')}/payment/finish`,
        notification: `${this.configService.get('BACKEND_URL')}/payments/notification`
      }
    };

    console.log('Creating Midtrans transaction with parameters:', JSON.stringify(parameter, null, 2));
    
    return this.snap.createTransaction(parameter);
  }

  async checkTransactionStatus(orderId: string) {
    try {
      return await this.snap.transaction.status(orderId);
    } catch (error) {
      console.error('Error checking transaction status:', error);
      
      // Check if error is due to transaction not existing
      if (error.httpStatusCode === 404 || 
          (error.message && error.message.includes("doesn't exist"))) {
        // Return a standardized response for non-existent transactions
        return {
          transaction_status: 'not_found',
          order_id: orderId,
          transaction_id: null
        };
      }
      
      throw new Error('Failed to check transaction status');
    }
  }

  async cancelTransaction(orderId: string) {
    try {
      return await this.snap.transaction.cancel(orderId);
    } catch (error) {
      console.error('Error cancelling transaction:', error);
      
      // If error is due to transaction not existing or already being cancelled, just return
      if (error.httpStatusCode === 404 || 
          (error.message && (error.message.includes("doesn't exist") || 
                            error.message.includes("already cancelled")))) {
        return {
          transaction_status: 'cancelled',
          order_id: orderId
        };
      }
      
      throw new Error('Failed to cancel transaction');
    }
  }

  // New methods for invoice functionality

  /**
   * Create an invoice for payment
   */
  async createInvoice(params: {
    invoiceId: string;
    amount: number;
    customerName: string;
    customerEmail: string;
    description: string;
    dueDate: Date;
    items: Array<{
      name: string;
      price: number;
      quantity: number;
    }>;
  }) {
    try {
      const { invoiceId, amount, customerName, customerEmail, description, dueDate, items } = params;
      
      // Format due date for Midtrans (ISO format)
      const formattedDueDate = dueDate.toISOString();
      
      console.log(`Creating invoice with formatted due date: ${formattedDueDate}`);
      
      const payload = {
        invoice_number: invoiceId,
        amount,
        description,
        due_date: formattedDueDate,
        expiry_duration: 24, // Set expiry to 24 hours
        expiry_unit: 'hour',
        customer: {
          name: customerName,
          email: customerEmail
        },
        items: items.map(item => ({
          name: item.name,
          price: item.price,
          quantity: item.quantity
        }))
      };
      
      console.log('Creating invoice with payload:', JSON.stringify(payload, null, 2));
      
      const response = await axios.post(
        `${this.irisApiUrl}/v1/invoices`,
        payload,
        {
          auth: {
            username: this.apiKey,
            password: ''
          },
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      console.log('Invoice created:', response.data);
      
      return {
        invoice_url: response.data.invoice_url,
        expiry_date: formattedDueDate
      };
    } catch (error) {
      console.error('Error creating invoice:', error.response?.data || error.message);
      throw new Error('Failed to create invoice');
    }
  }

  /**
   * Get invoice details by invoice ID
   */
  async getInvoice(invoiceId: string) {
    try {
      console.log(`Getting invoice details for: ${invoiceId}`);
      
      const response = await axios.get(
        `${this.irisApiUrl}/v1/invoices/${invoiceId}`,
        {
          auth: {
            username: this.apiKey,
            password: ''
          },
          headers: {
            'Accept': 'application/json'
          }
        }
      );

      console.log('Invoice details response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error getting invoice:', error.response?.data || error.message);
      
      // If the invoice is not found in Midtrans but exists in our database,
      // return a basic structure with available information
      if (error.response?.status === 404) {
        console.log('Invoice not found in Midtrans, returning basic info');
        return {
          status: 'unknown',
          message: 'Invoice details not available from payment provider'
        };
      }
      
      throw new Error('Failed to get invoice');
    }
  }

  /**
   * Get all invoices with optional filters
   */
  async getInvoices(params?: {
    from?: Date;
    to?: Date;
    status?: 'pending' | 'paid' | 'expired';
    page?: number;
    limit?: number;
  }) {
    try {
      const queryParams = new URLSearchParams();
      
      if (params?.from) {
        queryParams.append('from_date', params.from.toISOString().split('T')[0]);
      }
      
      if (params?.to) {
        queryParams.append('to_date', params.to.toISOString().split('T')[0]);
      }
      
      if (params?.status) {
        queryParams.append('status', params.status);
      }
      
      if (params?.page) {
        queryParams.append('page', params.page.toString());
      }
      
      if (params?.limit) {
        queryParams.append('limit', params.limit.toString());
      }

      const url = `${this.irisApiUrl}/v1/invoices?${queryParams.toString()}`;
      
      const response = await axios.get(url, {
        auth: {
          username: this.apiKey,
          password: ''
        },
        headers: {
          'Accept': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error getting invoices:', error.response?.data || error.message);
      throw new Error('Failed to get invoices');
    }
  }

  /**
   * Cancel an invoice
   */
  async cancelInvoice(invoiceId: string) {
    try {
      const response = await axios.delete(
        `${this.irisApiUrl}/v1/invoices/${invoiceId}`,
        {
          auth: {
            username: this.apiKey,
            password: ''
          },
          headers: {
            'Accept': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error canceling invoice:', error.response?.data || error.message);
      throw new Error('Failed to cancel invoice');
    }
  }

  /**
   * Handle invoice payment notification
   */
  async handleInvoiceNotification(notification: any) {
    try {
      // Verify the notification with Midtrans
      const invoiceId = notification.invoice_number;
      const invoiceStatus = await this.getInvoice(invoiceId);
      
      return {
        verified: true,
        invoiceId,
        status: invoiceStatus.status,
        paidAmount: invoiceStatus.paid_amount,
        paidAt: invoiceStatus.paid_at
      };
    } catch (error) {
      console.error('Error handling invoice notification:', error);
      throw new Error('Failed to handle invoice notification');
    }
  }
}
