import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('payments')
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  order_id: string;

  @Column()
  companyId: string;

  @Column('decimal')
  amount: number;

  @Column({ nullable: true })
  plan_id: string;

  @Column({ nullable: true })
  quantity: number;

  @Column({ nullable: true })
  duration: number;

  @Column({ nullable: true })
  valid_until: Date; // When subscription ends (after which it reverts to free)

  @Column({ nullable: true })
  payment_due_date: Date; // Payment deadline (H+1 from creation)

  @Column({ nullable: true })
  subscription_end_date: Date; // Redundant with valid_until, keeping for backward compatibility

  @Column()
  status: string;

  @Column({ nullable: true })
  payment_type: string;

  @Column({ nullable: true })
  transaction_id: string;

  @Column({ nullable: true })
  invoice_url: string;

  @Column({ nullable: true })
  cancellation_reason: string;

  @Column({ nullable: true })
  processed_with: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
