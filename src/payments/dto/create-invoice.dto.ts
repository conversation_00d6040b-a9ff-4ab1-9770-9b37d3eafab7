import { IsNotEmpty, IsString, <PERSON>N<PERSON>ber, <PERSON><PERSON>ptional, IsIn } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateInvoiceDto {
  @ApiProperty({ description: 'Plan ID', example: 'enterprise' })
  @IsString()
  @IsNotEmpty()
  @IsIn(['enterprise', 'topup'])
  planId: string;

  @ApiPropertyOptional({ description: 'Quantity (for topup plans)', example: 1 })
  @IsNumber()
  @IsOptional()
  quantity?: number;

  @ApiPropertyOptional({ description: 'Duration in months (for enterprise plans)', example: 12 })
  @IsNumber()
  @IsOptional()
  duration?: number;
}