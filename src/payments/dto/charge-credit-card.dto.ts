import { IsNotEmpty, <PERSON>String, <PERSON><PERSON><PERSON>ber, <PERSON>Optional, IsIn } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ChargeCreditCardDto {
  @ApiProperty({ description: 'Plan ID', example: 'enterprise' })
  @IsString()
  @IsNotEmpty()
  @IsIn(['enterprise', 'topup'])
  planId: string;

  @ApiProperty({ description: 'Credit card token from Midtrans', example: '48111111-1114-ffb81bf2-aa8f-47a2-9d1d-245c94d020e2' })
  @IsString()
  @IsNotEmpty()
  cardToken: string;

  @ApiPropertyOptional({ description: 'Quantity (for topup plans)', example: 1 })
  @IsNumber()
  @IsOptional()
  quantity?: number;

  @ApiPropertyOptional({ description: 'Duration in years (for enterprise plans)', example: 1 })
  @IsNumber()
  @IsOptional()
  duration?: number;
}
