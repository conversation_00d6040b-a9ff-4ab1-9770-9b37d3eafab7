import { Module } from '@nestjs/common';
import { PaymentsController } from './payments.controller';
import { MidtransService } from './midtrans.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { Company } from '../users/entities/company.entity';
import { Payment } from './entities/payment.entity';
import { SubscriptionsModule } from '../subscriptions/subscriptions.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Company, Payment]),
    SubscriptionsModule
  ],
  controllers: [PaymentsController],
  providers: [MidtransService],
  exports: [MidtransService]
})
export class PaymentsModule {}
