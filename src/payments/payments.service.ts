import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Payment } from './entities/payment.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import { LessThan } from 'typeorm';

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private paymentsRepository: Repository<Payment>,
  ) {}

  // Run every hour to check for expired invoices
  @Cron(CronExpression.EVERY_HOUR)
  async checkExpiredInvoices() {
    try {
      console.log('Checking for expired invoices...');
      
      // Find all pending invoices that have expired
      const now = new Date();
      const expiredInvoices = await this.paymentsRepository.find({
        where: {
          status: 'pending',
          payment_due_date: LessThan(now)
        }
      });
      
      console.log(`Found ${expiredInvoices.length} expired invoices`);
      
      // Update status to 'expired' for all expired invoices
      for (const invoice of expiredInvoices) {
        invoice.status = 'expired';
        await this.paymentsRepository.save(invoice);
        console.log(`Marked invoice ${invoice.order_id} as expired`);
      }
    } catch (error) {
      console.error('Error checking expired invoices:', error);
    }
  }

  // Run daily to check for expired subscriptions
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async checkExpiredSubscriptions() {
    try {
      console.log('Checking for expired subscriptions...');
      
      // Find all active subscriptions that have expired
      const now = new Date();
      const expiredSubscriptions = await this.paymentsRepository.find({
        where: {
          status: 'settlement',
          valid_until: LessThan(now)
        }
      });
      
      console.log(`Found ${expiredSubscriptions.length} expired subscriptions`);
      
      // Process each expired subscription
      for (const subscription of expiredSubscriptions) {
        console.log(`Processing expired subscription for company ${subscription.companyId}`);
        
        // Call subscription service to revert to free plan
        // This is a placeholder - you'll need to implement this in your subscription service
        // await this.subscriptionsService.revertToFreePlan(subscription.companyId);
        
        console.log(`Company ${subscription.companyId} reverted to Free plan`);
      }
    } catch (error) {
      console.error('Error checking expired subscriptions:', error);
    }
  }
}
