import { 
  Controller, 
  Post, 
  Body, 
  UseGuards, 
  Req, 
  BadRequestException,
  UnauthorizedException,
  InternalServerErrorException,
  Get,
  Param,
  NotFoundException,
  Query
} from '@nestjs/common';
import { MidtransService } from './midtrans.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Company } from '../users/entities/company.entity';
import { Payment } from './entities/payment.entity';
import { SubscriptionsService } from '../subscriptions/subscriptions.service';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { CreateTransactionDto } from './dto/create-transaction.dto';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { calculateSubscriptionEndDate } from '../utils/date.utils';
import { Not } from 'typeorm';

@ApiTags('payments')
@Controller('payments')
export class PaymentsController {
  constructor(
    private readonly midtransService: MidtransService,
    private readonly subscriptionsService: SubscriptionsService,
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    @InjectRepository(Company)
    private readonly companiesRepository: Repository<Company>,
    @InjectRepository(Payment)
    private readonly paymentsRepository: Repository<Payment>,
    private readonly configService: ConfigService
  ) {}

  @Post('create-transaction')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a payment transaction' })
  @ApiResponse({ status: 201, description: 'Transaction created successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async createTransaction(@Body() createTransactionDto: CreateTransactionDto, @Req() req: any) {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        throw new UnauthorizedException('User not authenticated');
      }
      
      const { planId, quantity = 1, duration = 1 } = createTransactionDto;
      const user = req.user;
      
      // Check if user has a company
      if (!user.company) {
        throw new BadRequestException('User does not have an associated company');
      }

      // Create a shorter order ID
      let orderId;
      if (planId === 'enterprise') {
        // Format: ORD-ent-{duration}-{user_id_short}-{timestamp}-{random}
        const userIdShort = user.id.replace(/-/g, '').substring(0, 8);
        const timestamp = Date.now().toString().slice(-8);
        const randomStr = Math.random().toString(36).substring(2, 6);
        orderId = `ORD-ent-${duration}-${userIdShort}-${timestamp}-${randomStr}`;
      } else if (planId === 'topup') {
        // Format: ORD-top-{quantity}-{user_id_short}-{timestamp}-{random}
        const userIdShort = user.id.replace(/-/g, '').substring(0, 8);
        const timestamp = Date.now().toString().slice(-8);
        const randomStr = Math.random().toString(36).substring(2, 6);
        orderId = `ORD-top-${quantity}-${userIdShort}-${timestamp}-${randomStr}`;
      } else {
        throw new BadRequestException('Invalid plan ID');
      }
      
      // Calculate amount based on plan
      let amount = 0;
      let description = '';
      let itemName = '';
      
      if (planId === 'enterprise') {
        amount = 3500000 * duration; // Rp 3,500,000 per month //coba
        description = `Enterprise Plan Subscription (${duration} ${duration === 1 ? 'month' : 'months'})`;
        itemName = `Enterprise Plan (${duration} ${duration === 1 ? 'month' : 'months'})`;
      } else if (planId === 'topup') {
        amount = 1000000 * quantity; // Rp 1,000,000 per 1M tokens
        description = `Token Top-up (${quantity}M tokens)`;
        itemName = `Token Top-up (${quantity}M tokens)`;
      }
      
      // Log transaction attempt for debugging
      console.log(`Creating transaction for user ${user.id}, plan ${planId}, amount ${amount}, orderId ${orderId}`);
      
      const transaction = await this.midtransService.createTransaction({
        orderId,
        amount,
        customerDetails: {
          firstName: user.name || 'Customer',
          email: user.email
        },
        itemDetails: [{
          id: planId,
          price: amount,
          quantity: 1,
          name: itemName
        }]
      });

      // Calculate subscription end date for enterprise plans
      let subscriptionEndDate = null;
      if (planId === 'enterprise') {
        // Get the current subscription to check if we're extending
        const currentSubscription = await this.subscriptionsService.getCompanySubscription(user.company.id);
        
        if (currentSubscription && 
            currentSubscription.subscription.name === 'Enterprise' && 
            currentSubscription.end_date && 
            new Date(currentSubscription.end_date) > new Date()) {
          // If there's an active enterprise subscription, extend from current end date
          subscriptionEndDate = new Date(currentSubscription.end_date);
          subscriptionEndDate.setMonth(subscriptionEndDate.getMonth() + duration);
        } else {
          // Otherwise, calculate from today
          subscriptionEndDate = calculateSubscriptionEndDate(duration);
        }
        
        console.log(`Calculated subscription end date: ${subscriptionEndDate}`);
      }
      
      // Create payment record
      const payment = this.paymentsRepository.create({
        order_id: orderId,
        companyId: user.company.id,
        amount: amount,
        plan_id: planId,
        quantity: planId === 'topup' ? quantity : null,
        duration: planId === 'enterprise' ? duration : null,
        valid_until: subscriptionEndDate,
        status: 'pending',
        invoice_url: transaction.redirect_url
      });
      
      await this.paymentsRepository.save(payment);

      return {
        token: transaction.token,
        redirect_url: transaction.redirect_url,
        orderId
      };
    } catch (error) {
      console.error('Error creating transaction:', error);
      if (error instanceof BadRequestException || error instanceof UnauthorizedException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create transaction');
    }
  }

  @Post('notification')
  @ApiOperation({ summary: 'Handle payment notification from Midtrans' })
  async handleNotification(@Body() notification: any) {
    try {
      console.log('==========================================');
      console.log('RECEIVED PAYMENT NOTIFICATION:');
      console.log(JSON.stringify(notification, null, 2));
      console.log('==========================================');
      
      // Verify notification from Midtrans
      const { order_id, transaction_status, fraud_status, payment_type } = notification;
      
      console.log(`Payment status for ${order_id}: ${transaction_status}, fraud: ${fraud_status}`);
      
      // Verify the notification with Midtrans
      let verifiedStatus;
      try {
        verifiedStatus = await this.midtransService.checkTransactionStatus(order_id);
        console.log('Verified status from Midtrans:', JSON.stringify(verifiedStatus, null, 2));
      } catch (verifyError) {
        console.error('Error verifying transaction status:', verifyError);
        return { status: 'error', message: 'Failed to verify transaction status' };
      }
      
      // If transaction doesn't exist in Midtrans, mark as failed
      if (verifiedStatus.transaction_status === 'not_found') {
        const payment = await this.paymentsRepository.findOne({ where: { order_id } });
        if (payment) {
          payment.status = 'failed';
          await this.paymentsRepository.save(payment);
        }
        return { status: 'error', message: 'Transaction not found in payment provider system' };
      }
      
      // Find payment in database
      const payment = await this.paymentsRepository.findOne({ where: { order_id } });
      
      if (!payment) {
        console.error(`Payment not found for order ID: ${order_id}`);
        return { status: 'error', message: 'Payment not found' };
      }
      
      // Update payment status
      payment.status = transaction_status;
      payment.payment_type = payment_type;
      
      if (verifiedStatus.transaction_id) {
        payment.transaction_id = verifiedStatus.transaction_id;
      }
      
      await this.paymentsRepository.save(payment);
      
      if (transaction_status === 'capture' || transaction_status === 'settlement') {
        if (fraud_status === 'accept') {
          console.log(`Payment success for order ${order_id}, updating subscription`);
          
          // Parse order ID to extract information
          const parts = order_id.split('-');
          
          if (parts.length < 5) {
            console.error(`Invalid order ID format: ${order_id}`);
            return { status: 'error', message: 'Invalid order ID format' };
          }
          
          // Extract plan type and other details
          const planPrefix = parts[1];
          let planId;
          let quantity = 1;
          let duration = 1;
          let userIdShortIndex;
          
          if (planPrefix === 'ent') {
            planId = 'enterprise';
            // For enterprise, the format is ORD-ent-{duration}-{user_id_short}-{timestamp}-{random}
            duration = parseInt(parts[2], 10) || 1;
            userIdShortIndex = 3; // User ID is at position 3
          } else if (planPrefix === 'top') {
            planId = 'topup';
            // For topup, the format is ORD-top-{quantity}-{user_id_short}-{timestamp}-{random}
            quantity = parseInt(parts[2], 10) || 1;
            userIdShortIndex = 3; // User ID is at position 3
          } else {
            // For older format, maintain backward compatibility
            if (planPrefix === 'ent') {
              planId = 'enterprise';
              userIdShortIndex = 2;
            } else if (planPrefix === 'top') {
              planId = 'topup';
              // For topup, the quantity is included in the order ID
              if (parts.length >= 6) {
                quantity = parseInt(parts[2], 10) || 1;
                userIdShortIndex = 3; // User ID is shifted one position
              } else {
                userIdShortIndex = 2;
              }
            } else {
              userIdShortIndex = 2;
            }
          }
          
          // Extract user ID short
          const userIdShort = parts[userIdShortIndex];
          
          // Find the user by the shortened ID
          const users = await this.usersRepository.find();
          const user = users.find(u => u.id.replace(/-/g, '').substring(0, 8) === userIdShort);
          
          if (!user) {
            console.error(`User not found with ID prefix: ${userIdShort}`);
            return { status: 'error', message: 'User not found' };
          }
          
          // Get user with company
          const userWithCompany = await this.usersRepository.findOne({
            where: { id: user.id },
            relations: ['company']
          });
          
          if (!userWithCompany || !userWithCompany.company) {
            console.error(`User has no company: ${user.id}`);
            return { status: 'error', message: 'User has no company' };
          }
          
          const companyId = userWithCompany.company.id;
          
          try {
            // Process based on plan type
            if (planId === 'enterprise') {
              // Upgrade to enterprise with the specified duration
              const subscription = await this.subscriptionsService.upgradeToEnterprise(companyId, duration);
              console.log(`Upgraded to enterprise for company ${companyId} for ${duration} months`, subscription);
              
              // Force update the company subscription in the database
              await this.subscriptionsService.setActiveSubscription(companyId, subscription.id);
            } else if (planId === 'topup') {
              // Top up tokens
              const subscription = await this.subscriptionsService.topUpTokens(companyId, quantity * 1000000);
              console.log(`Topped up ${quantity}M tokens for company ${companyId}`, subscription);
            }
            
            return { status: 'ok', message: 'Payment processed successfully' };
          } catch (error) {
            console.error('Error updating subscription:', error);
            return { status: 'error', message: `Error updating subscription: ${error.message}` };
          }
        }
      }
      
      return { status: 'ok', message: 'Notification received' };
    } catch (error) {
      console.error('Error handling notification:', error);
      return { status: 'error', message: 'Error processing notification' };
    }
  }

  @Get('check-status/:orderId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Check payment status' })
  async checkPaymentStatus(@Param('orderId') orderId: string, @Req() req: any) {
    try {
      console.log(`Checking payment status for order: ${orderId}`);
      
      // Check payment status with Midtrans
      const status = await this.midtransService.checkTransactionStatus(orderId);
      console.log('Midtrans status response:', JSON.stringify(status, null, 2));
      
      // If transaction doesn't exist in Midtrans, mark as failed instead of pending
      if (status.transaction_status === 'not_found') {
        const payment = await this.paymentsRepository.findOne({ where: { order_id: orderId } });
        await this.paymentsRepository.save(payment);
        return {
          status: 'error',
          message: 'Payment not found in payment provider system',
          paymentStatus: 'failed'
        };
      }
      
      // Parse order ID to extract information
      const parts = orderId.split('-');
      
      if (parts.length < 5) {
        throw new BadRequestException('Invalid order ID format');
      }
      
      // Extract plan type and other details
      const planPrefix = parts[1];
      let planId;
      let quantity = 1;
      let duration = 1;
      let userIdShortIndex;
      
      if (planPrefix === 'ent') {
        planId = 'enterprise';
        // For enterprise, the format is ORD-ent-{duration}-{user_id_short}-{timestamp}-{random}
        duration = parseInt(parts[2], 10) || 1;
        userIdShortIndex = 3; // User ID is at position 3
      } else if (planPrefix === 'top') {
        planId = 'topup';
        // For topup, the format is ORD-top-{quantity}-{user_id_short}-{timestamp}-{random}
        quantity = parseInt(parts[2], 10) || 1;
        userIdShortIndex = 3; // User ID is at position 3
      } else {
        // For older format, maintain backward compatibility
        if (planPrefix === 'ent') {
          planId = 'enterprise';
        } else if (planPrefix === 'top') {
          planId = 'topup';
          // For topup, the quantity is included in the order ID
          if (parts.length >= 6) {
            quantity = parseInt(parts[2], 10) || 1;
            userIdShortIndex = 3; // User ID is shifted one position
          } else {
            userIdShortIndex = 2;
          }
        } else {
          userIdShortIndex = 2;
        }
      }
      
      // Extract user ID short
      const userIdShort = parts[userIdShortIndex];
      
      // Find the user by the shortened ID
      const users = await this.usersRepository.find();
      const user = users.find(u => u.id.replace(/-/g, '').substring(0, 8) === userIdShort);
      
      if (!user) {
        throw new NotFoundException(`User not found with ID prefix: ${userIdShort}`);
      }
      
      // Get user with company
      const userWithCompany = await this.usersRepository.findOne({
        where: { id: user.id },
        relations: ['company']
      });
      
      if (!userWithCompany || !userWithCompany.company) {
        throw new NotFoundException('User has no company');
      }
      
      const companyId = userWithCompany.company.id;
      
      // Find payment in database
      const payment = await this.paymentsRepository.findOne({ where: { order_id: orderId } });
      
      // If payment exists, update its status
      if (payment) {
        payment.status = status.transaction_status;
        payment.payment_type = status.payment_type;
        
        if (status.transaction_id) {
          payment.transaction_id = status.transaction_id;
        }
        
        await this.paymentsRepository.save(payment);
      }
      
      // If payment is successful but subscription not updated, update it now
      if ((status.transaction_status === 'settlement' || status.transaction_status === 'capture')) {
        console.log('Payment is successful. Ensuring subscription is updated...');
        
        // Cancel any pending transactions for the same plan
        await this.cancelPendingTransactionsForSamePlan(companyId, planId, orderId);
        
        // Process based on plan type
        if (planId === 'enterprise') {
          // Upgrade to enterprise with the specified duration
          const updatedSubscription = await this.subscriptionsService.upgradeToEnterprise(
            companyId, 
            duration,
            payment?.valid_until
          );
          console.log(`Upgraded to enterprise for company ${companyId} for ${duration} months`, updatedSubscription);
          
          // Ensure this subscription is set as active
          await this.subscriptionsService.setActiveSubscription(companyId, updatedSubscription.id);
          
          return {
            status: 'success',
            message: 'Payment verified and subscription updated',
            paymentStatus: status.transaction_status
          };
        } else if (planId === 'topup') {
          // Top up tokens
          const updatedSubscription = await this.subscriptionsService.topUpTokens(
            companyId, 
            (payment?.quantity || 1) * 1000000
          );
          console.log(`Topped up ${payment?.quantity || 1}M tokens for company ${companyId}`, updatedSubscription);
          
          return {
            status: 'success',
            message: 'Payment verified and subscription updated',
            paymentStatus: status.transaction_status
          };
        }
      }
      
      return {
        status: 'pending',
        message: 'Payment is still being processed',
        paymentStatus: status.transaction_status
      };
    } catch (error) {
      console.error('Error checking payment status:', error);
      throw new InternalServerErrorException('Failed to check payment status');
    }
  }

  @Post('manual-update/:orderId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Manually update subscription for a payment' })
  async manualUpdate(@Param('orderId') orderId: string, @Req() req: any) {
    try {
      console.log(`Manual update requested for order: ${orderId}`);
      
      // First, verify the payment status with Midtrans
      let status;
      try {
        status = await this.midtransService.checkTransactionStatus(orderId);
        console.log('Midtrans status for manual update:', JSON.stringify(status, null, 2));
      } catch (error) {
        console.error('Error checking transaction status:', error);
        return {
          status: 'error',
          message: 'Failed to verify payment status with payment provider'
        };
      }
      
      // If transaction doesn't exist in Midtrans, mark as failed
      if (status.transaction_status === 'not_found') {
        const payment = await this.paymentsRepository.findOne({ where: { order_id: orderId } });
        if (payment) {
          payment.status = 'failed';
          await this.paymentsRepository.save(payment);
        }
        
        return {
          status: 'error',
          message: 'Transaction not found in payment provider system',
          paymentStatus: 'failed'
        };
      }
      
      // Only proceed if payment is actually completed
      if (status.transaction_status !== 'settlement' && 
          status.transaction_status !== 'capture') {
        return {
          status: 'error',
          message: `Payment has not been completed. Current status: ${status.transaction_status}`,
          paymentStatus: status.transaction_status
        };
      }
      
      // Parse order ID to extract information
      const parts = orderId.split('-');
      
      if (parts.length < 5) {
        throw new BadRequestException('Invalid order ID format');
      }
      
      // Extract plan type and other details
      const planPrefix = parts[1];
      let planId;
      let quantity = 1;
      let duration = 1;
      let userIdShortIndex;
      
      if (planPrefix === 'ent') {
        planId = 'enterprise';
        // For enterprise, the format is ORD-ent-{duration}-{user_id_short}-{timestamp}-{random}
        duration = parseInt(parts[2], 10) || 1;
        userIdShortIndex = 3; // User ID is at position 3
      } else if (planPrefix === 'top') {
        planId = 'topup';
        // For topup, the format is ORD-top-{quantity}-{user_id_short}-{timestamp}-{random}
        quantity = parseInt(parts[2], 10) || 1;
        userIdShortIndex = 3; // User ID is at position 3
      } else {
        // For older format, maintain backward compatibility
        if (planPrefix === 'ent') {
          planId = 'enterprise';
          userIdShortIndex = 2;
        } else if (planPrefix === 'top') {
          planId = 'topup';
          // For topup, the quantity is included in the order ID
          if (parts.length >= 6) {
            quantity = parseInt(parts[2], 10) || 1;
            userIdShortIndex = 3; // User ID is shifted one position
          } else {
            userIdShortIndex = 2;
          }
        } else {
          userIdShortIndex = 2;
        }
      }
      
      // Extract user ID short
      const userIdShort = parts[userIdShortIndex];
      
      // Find the user by the shortened ID
      const users = await this.usersRepository.find();
      const user = users.find(u => u.id.replace(/-/g, '').substring(0, 8) === userIdShort);
      
      if (!user) {
        throw new NotFoundException(`User not found with ID prefix: ${userIdShort}`);
      }
      
      // Get user with company
      const userWithCompany = await this.usersRepository.findOne({
        where: { id: user.id },
        relations: ['company']
      });
      
      if (!userWithCompany || !userWithCompany.company) {
        throw new NotFoundException('User has no company');
      }
      
      const companyId = userWithCompany.company.id;
      
      // Find payment in database
      const payment = await this.paymentsRepository.findOne({ where: { order_id: orderId } });
      
      // If payment exists, update its status
      if (payment) {
        payment.status = status.transaction_status;
        payment.payment_type = status.payment_type;
        
        if (status.transaction_id) {
          payment.transaction_id = status.transaction_id;
        }
        
        await this.paymentsRepository.save(payment);
      }
      
      // Process based on plan type
      if (planId === 'enterprise') {
        // Upgrade to enterprise with the specified duration
        const updatedSubscription = await this.subscriptionsService.upgradeToEnterprise(
          companyId, 
          duration,
          payment.valid_until
        );
        console.log(`Manually upgraded to enterprise for company ${companyId} for ${duration} months`, updatedSubscription);
      } else if (planId === 'topup') {
        // Top up tokens
        const updatedSubscription = await this.subscriptionsService.topUpTokens(
          companyId, 
          quantity * 1000000
        );
        console.log(`Manually topped up ${quantity}M tokens for company ${companyId}`, updatedSubscription);
      }
      
      return {
        status: 'success',
        message: 'Subscription manually updated successfully',
        paymentStatus: status.transaction_status
      };
    } catch (error) {
      console.error('Error manually updating subscription:', error);
      if (error instanceof NotFoundException) {
        throw error;
      } else if (error instanceof BadRequestException) {
        throw error;
      } else {
        throw new InternalServerErrorException('Failed to manually update subscription');
      }
    }
  }

  @Get('history')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get payment history' })
  async getPaymentHistory(@Req() req: any, @Query('page') page = 1, @Query('limit') limit = 10) {
    try {
      const user = req.user;
      
      // Load the user with company relation
      const userWithCompany = await this.usersRepository.findOne({
        where: { id: user.id },
        relations: ['company']
      });
      
      if (!userWithCompany || !userWithCompany.company) {
        throw new NotFoundException('User has no company');
      }
      
      const companyId = userWithCompany.company.id;
      
      // Get payment history from database
      const [payments, total] = await this.paymentsRepository.findAndCount({
        where: { companyId: companyId },
        order: { created_at: 'DESC' },
        skip: (page - 1) * limit,
        take: limit
      });
      
      // For each payment, check if it's still pending and update if needed
      for (const payment of payments) {
        if (payment.status === 'pending') {
          try {
            const status = await this.midtransService.checkTransactionStatus(payment.order_id);
            
            // If status has changed, update it
            if (status.transaction_status !== payment.status) {
              payment.status = status.transaction_status;
              await this.paymentsRepository.save(payment);
              
              // If payment is now successful, update subscription
              if (status.transaction_status === 'settlement' || status.transaction_status === 'capture') {
                // Parse order ID to extract information
                const parts = payment.order_id.split('-');
                
                if (parts.length >= 5) {
                  const planPrefix = parts[1];
                  let planId;
                  let quantity = 1;
                  
                  if (planPrefix === 'ent') {
                    planId = 'enterprise';
                  } else if (planPrefix === 'top') {
                    planId = 'topup';
                    if (parts.length >= 6) {
                      quantity = parseInt(parts[2], 10) || 1;
                    }
                  }
                  
                  // Update subscription based on plan
                  if (planId === 'enterprise') {
                    await this.subscriptionsService.upgradeToEnterprise(companyId);
                  } else if (planId === 'topup') {
                    await this.subscriptionsService.topUpTokens(companyId, quantity * 1000000);
                  }
                }
              }
            }
          } catch (error) {
            console.error(`Error checking status for payment ${payment.order_id}:`, error);
          }
        }
      }
      
      return {
        items: payments,
        meta: {
          totalItems: total,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };
    } catch (error) {
      console.error('Error getting payment history:', error);
      throw new InternalServerErrorException('Failed to get payment history');
    }
  }

  @Get('details/:orderId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get payment details' })
  async getPaymentDetails(@Param('orderId') orderId: string, @Req() req: any) {
    try {
      // Find payment in database
      const payment = await this.paymentsRepository.findOne({ where: { order_id: orderId } });
      
      if (!payment) {
        throw new NotFoundException('Payment not found');
      }
      
      // Check if the payment belongs to the user's company
      const user = req.user;
      const userWithCompany = await this.usersRepository.findOne({
        where: { id: user.id },
        relations: ['company']
      });
      
      if (!userWithCompany || !userWithCompany.company) {
        throw new NotFoundException('User has no company');
      }
      
      if (payment.companyId !== userWithCompany.company.id) {
        throw new UnauthorizedException('Payment does not belong to your company');
      }
      
      // Check current status with Midtrans
      try {
        const status = await this.midtransService.checkTransactionStatus(orderId);
        
        // Update payment status if it has changed
        if (status.transaction_status !== payment.status) {
          payment.status = status.transaction_status;
          await this.paymentsRepository.save(payment);
        }
      } catch (error) {
        console.error('Error checking payment status:', error);
        // Continue with the existing payment data
      }
      
      return payment;
    } catch (error) {
      console.error('Error getting payment details:', error);
      throw new InternalServerErrorException('Failed to get payment details');
    }
  }

  @Post('resume/:orderId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Resume payment' })
  async resumePayment(@Param('orderId') orderId: string, @Req() req: any) {
    try {
      // Find payment in database
      const payment = await this.paymentsRepository.findOne({ where: { order_id: orderId } });
      
      if (!payment) {
        throw new NotFoundException('Payment not found');
      }
      
      // Check if the payment belongs to the user's company
      const user = req.user;
      const userWithCompany = await this.usersRepository.findOne({
        where: { id: user.id },
        relations: ['company']
      });
      
      if (!userWithCompany || !userWithCompany.company) {
        throw new NotFoundException('User has no company');
      }
      
      if (payment.companyId !== userWithCompany.company.id) {
        throw new UnauthorizedException('Payment does not belong to your company');
      }
      
      // Check current status with Midtrans
      const status = await this.midtransService.checkTransactionStatus(orderId);
      
      // If payment is already completed, return success
      if (status.transaction_status === 'settlement' || status.transaction_status === 'capture') {
        return {
          status: 'success',
          message: 'Payment already completed',
          redirect_url: `${this.configService.get('FRONTEND_URL')}/payment/finish?order_id=${orderId}&transaction_status=${status.transaction_status}`
        };
      }
      
      // If payment is still pending and has a redirect URL, return it
      if (payment.invoice_url) {
        return {
          status: 'success',
          redirect_url: payment.invoice_url
        };
      }
      
      // Otherwise, recreate the transaction
      let planId = payment.plan_id;
      let quantity = payment.quantity || 1;
      
      let amount = 0;
      let itemName = '';
      
      if (planId === 'enterprise') {
        amount = 3500000; // Rp 42,000,000 //coba
        itemName = 'Enterprise Plan (1 year)';
      } else if (planId === 'topup') {
        amount = 1000000 * quantity; // Rp 1,000,000 per 1M tokens
        itemName = `Token Top-up (${quantity}M tokens)`;
      }
      
      const transaction = await this.midtransService.createTransaction({
        orderId: orderId,
        amount: amount,
        customerDetails: {
          firstName: userWithCompany.name || 'Customer',
          email: userWithCompany.email
        },
        itemDetails: [{
          id: planId,
          price: amount,
          quantity: 1,
          name: itemName
        }]
      });
      
      // Update payment with new invoice URL
      payment.invoice_url = transaction.redirect_url;
      await this.paymentsRepository.save(payment);
      
      return {
        status: 'success',
        redirect_url: transaction.redirect_url
      };
    } catch (error) {
      console.error('Error resuming payment:', error);
      throw new InternalServerErrorException('Failed to resume payment');
    }
  }

  @Post('create-invoice')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create an invoice for payment' })
  @ApiResponse({ status: 201, description: 'Invoice created successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async createInvoice(@Body() createInvoiceDto: CreateInvoiceDto, @Req() req: any) {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        throw new UnauthorizedException('User not authenticated');
      }
      
      const { planId, quantity = 1, duration = 1 } = createInvoiceDto;
      const user = req.user;
      
      // Check if user has a company
      if (!user.company) {
        throw new BadRequestException('User does not have an associated company');
      }
      
      // Create invoice ID
      const invoiceId = `INV-${planId === 'enterprise' ? 'ENT' : 'TOP'}-${uuidv4().substring(0, 8)}`;
      
      // Calculate amount based on plan
      let amount = 0;
      let description = '';
      let itemName = '';
      
      // Calculate subscription end date for enterprise plans
      let subscriptionEndDate = null;
      
      if (planId === 'enterprise') {
        amount = 3500000 * duration; // Rp 3,500,000 per month //coba
        description = `Enterprise Plan Subscription (${duration} ${duration === 1 ? 'month' : 'months'})`;
        itemName = `Enterprise Plan (${duration} ${duration === 1 ? 'month' : 'months'})`;
        
        // Use the utility function for consistent date calculation
        subscriptionEndDate = calculateSubscriptionEndDate(duration);
      } else if (planId === 'topup') {
        amount = 1000000 * quantity; // Rp 1,000,000 per 1M tokens
        description = `Token Top-up (${quantity}M tokens)`;
        itemName = `Token Top-up (${quantity}M tokens)`;
      } else {
        throw new BadRequestException('Invalid plan ID');
      }
      
      // Set payment due date (24 hours from now)
      const now = new Date();
      const paymentDueDate = new Date(now);
      paymentDueDate.setDate(paymentDueDate.getDate() + 1); // Add exactly 1 day

      console.log(`Creating invoice with payment due date: ${paymentDueDate.toISOString()}`);
      if (subscriptionEndDate) {
        console.log(`Subscription end date: ${subscriptionEndDate.toISOString()}`);
      }
      
      // Create invoice using Midtrans service
      const invoice = await this.midtransService.createInvoice({
        invoiceId,
        amount,
        customerName: user.name || 'Customer',
        customerEmail: user.email,
        description,
        dueDate: paymentDueDate, // Use payment due date (H+1) for the invoice
        items: [{
          name: itemName,
          price: amount,
          quantity: 1
        }]
      });
      
      // Store invoice information in database
      const payment = this.paymentsRepository.create({
        order_id: invoiceId,
        companyId: user.company.id,
        amount: amount,
        plan_id: planId,
        quantity: planId === 'topup' ? quantity : null,
        duration: planId === 'enterprise' ? duration : null,
        payment_due_date: paymentDueDate, // Set payment_due_date to H+1
        valid_until: subscriptionEndDate, // Set valid_until to when subscription ends
        subscription_end_date: subscriptionEndDate, // Keep for backward compatibility
        status: 'pending',
        invoice_url: invoice.invoice_url
      });
      
      await this.paymentsRepository.save(payment);
      
      return {
        invoiceId,
        invoiceUrl: invoice.invoice_url,
        amount,
        dueDate: paymentDueDate
      };
    } catch (error) {
      console.error('Error creating invoice:', error);
      if (error instanceof BadRequestException || error instanceof UnauthorizedException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create invoice');
    }
  }

  @Get('invoices')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all invoices for the current user' })
  @ApiResponse({ status: 200, description: 'List of invoices' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getInvoices(
    @Req() req: any,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('status') status?: 'pending' | 'paid' | 'expired'
  ) {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        throw new UnauthorizedException('User not authenticated');
      }
      
      const user = req.user;
      
      // Check if user has a company
      if (!user.company) {
        throw new BadRequestException('User does not have an associated company');
      }
      
      // Get invoices from database
      const [payments, total] = await this.paymentsRepository.findAndCount({
        where: { 
          companyId: user.company.id,
          ...(status ? { status } : {})
        },
        order: { created_at: 'DESC' },
        skip: (page - 1) * limit,
        take: limit
      });
      
      // For each payment, check if it's still pending and update if needed
      for (const payment of payments) {
        if (payment.status === 'pending' && payment.order_id.startsWith('INV-')) {
          try {
            const invoiceStatus = await this.midtransService.getInvoice(payment.order_id);
            
            // If status has changed, update it
            if (invoiceStatus.status !== payment.status) {
              payment.status = invoiceStatus.status;
              await this.paymentsRepository.save(payment);
              
              // If payment is now successful, update subscription
              if (invoiceStatus.status === 'paid') {
                // Parse invoice ID to extract information
                const parts = payment.order_id.split('-');
                
                if (parts.length >= 3) {
                  const planPrefix = parts[1];
                  let planId;
                  let quantity = 1;
                  
                  if (planPrefix === 'ENT') {
                    planId = 'enterprise';
                  } else if (planPrefix === 'TOP') {
                    planId = 'topup';
                    quantity = payment.quantity || 1;
                  }
                  
                  // Update subscription based on plan
                  if (planId === 'enterprise') {
                    await this.subscriptionsService.upgradeToEnterprise(user.company.id);
                  } else if (planId === 'topup') {
                    await this.subscriptionsService.topUpTokens(user.company.id, quantity * 1000000);
                  }
                }
              }
            }
          } catch (error) {
            console.error(`Error checking invoice status for ${payment.order_id}:`, error);
          }
        }
      }
      
      return {
        items: payments,
        meta: {
          totalItems: total,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };
    } catch (error) {
      console.error('Error getting invoices:', error);
      if (error instanceof UnauthorizedException) {
        throw error;
      } else if (error instanceof BadRequestException) {
        throw error;
      } else {
        throw new InternalServerErrorException('Failed to get invoices');
      }
    }
  }

  @Get('invoice/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get invoice details by ID' })
  @ApiResponse({ status: 200, description: 'Invoice details retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Invoice not found' })
  async getInvoiceById(@Param('id') id: string, @Req() req: any) {
    try {
      console.log(`Getting invoice details for ID: ${id}`);
      
      // Find payment in database
      const payment = await this.paymentsRepository.findOne({ 
        where: { order_id: id } 
      });
      
      if (!payment) {
        console.log(`Invoice not found in database: ${id}`);
        throw new NotFoundException('Invoice not found');
      }
      
      // Check if user has permission to view this invoice
      const user = req.user;
      const userWithCompany = await this.usersRepository.findOne({
        where: { id: user.id },
        relations: ['company']
      });
      
      if (!userWithCompany || !userWithCompany.company) {
        throw new NotFoundException('User has no company');
      }
      
      if (payment.companyId !== userWithCompany.company.id) {
        throw new UnauthorizedException('Payment does not belong to your company');
      }
      
      // Get invoice details from Midtrans
      let invoiceDetails;
      try {
        invoiceDetails = await this.midtransService.getInvoice(id);
      } catch (error) {
        console.error('Error fetching invoice details from payment provider:', error);
        // If we can't get details from Midtrans, continue with basic info
        invoiceDetails = {
          status: payment.status,
          expiry_date: payment.valid_until,
          message: 'Invoice details not available from payment provider'
        };
      }
      
      return {
        ...payment,
        invoiceDetails
      };
    } catch (error) {
      console.error('Error getting invoice details:', error);
      if (error instanceof NotFoundException || error instanceof UnauthorizedException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get invoice details');
    }
  }

  @Post('cancel-invoice/:invoiceId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Cancel an invoice' })
  @ApiResponse({ status: 200, description: 'Invoice cancelled successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Invoice not found' })
  async cancelInvoice(@Param('invoiceId') invoiceId: string, @Req() req: any) {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        throw new UnauthorizedException('User not authenticated');
      }
      
      // Find payment in database
      const payment = await this.paymentsRepository.findOne({ where: { order_id: invoiceId } });
      
      if (!payment) {
        throw new NotFoundException('Invoice not found');
      }
      
      // Check if the payment belongs to the user's company
      const user = req.user;
      const userWithCompany = await this.usersRepository.findOne({
        where: { id: user.id },
        relations: ['company']
      });
      
      if (!userWithCompany || !userWithCompany.company) {
        throw new NotFoundException('User has no company');
      }
      
      if (payment.companyId !== userWithCompany.company.id) {
        throw new UnauthorizedException('Invoice does not belong to your company');
      }
      
      // Only allow cancellation of pending invoices
      if (payment.status !== 'pending') {
        throw new BadRequestException('Only pending invoices can be cancelled');
      }
      
      // Cancel invoice with Midtrans
      if (invoiceId.startsWith('INV-')) {
        await this.midtransService.cancelInvoice(invoiceId);
        
        // Update payment status in database
        payment.status = 'cancelled';
        await this.paymentsRepository.save(payment);
        
        return {
          status: 'success',
          message: 'Invoice cancelled successfully'
        };
      }
      
      return {
        status: 'error',
        message: 'Invalid invoice ID format'
      };
    } catch (error) {
      console.error('Error cancelling invoice:', error);
      if (error instanceof UnauthorizedException) {
        throw error;
      } else if (error instanceof NotFoundException) {
        throw error;
      } else if (error instanceof BadRequestException) {
        throw error;
      } else {
        throw new InternalServerErrorException('Failed to cancel invoice');
      }
    }
  }

  @Post('process-payment')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Process payment using transaction or invoice' })
  async processPayment(@Body() body: any, @Req() req: any) {
    try {
      const { planId, quantity = 1, duration = 1, paymentMethod } = body;
      const user = req.user;
      
      // Check if user has a company
      if (!user.company) {
        throw new BadRequestException('User does not have an associated company');
      }
      
      // Determine payment method - invoice or immediate transaction
      if (paymentMethod === 'invoice') {
        // Create an invoice for later payment
        return this.createInvoice(body, req);
      } else {
        // Process immediate payment via Snap
        return this.createTransaction(body, req);
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      throw new InternalServerErrorException('Failed to process payment');
    }
  }

  @Post('invoice-notification')
  @ApiOperation({ summary: 'Handle invoice payment notification from Midtrans' })
  async handleInvoiceNotification(@Body() notification: any) {
    try {
      console.log('Received invoice notification:', JSON.stringify(notification, null, 2));
      
      // Verify and process the notification
      const result = await this.midtransService.handleInvoiceNotification(notification);
      
      if (!result.verified) {
        console.error('Failed to verify invoice notification');
        return { status: 'error', message: 'Failed to verify notification' };
      }
      
      // Find payment in database
      const payment = await this.paymentsRepository.findOne({ 
        where: { order_id: result.invoiceId } 
      });
      
      if (!payment) {
        console.error(`Payment not found for invoice ID: ${result.invoiceId}`);
        return { status: 'error', message: 'Payment not found' };
      }
      
      // Update payment status
      payment.status = result.status;
      await this.paymentsRepository.save(payment);
      
      // If payment is successful, update subscription
      if (result.status === 'paid') {
        // Parse invoice ID to extract information
        const parts = result.invoiceId.split('-');
        
        if (parts.length >= 3) {
          const planPrefix = parts[1];
          let planId;
          let quantity = 1;
          
          if (planPrefix === 'ENT') {
            planId = 'enterprise';
          } else if (planPrefix === 'TOP') {
            planId = 'topup';
            quantity = payment.quantity || 1;
          }
          
          // Update subscription based on plan
          if (planId === 'enterprise') {
            await this.subscriptionsService.upgradeToEnterprise(payment.companyId);
          } else if (planId === 'topup') {
            await this.subscriptionsService.topUpTokens(payment.companyId, quantity * 1000000);
          }
        }
      }
      
      return { status: 'ok', message: 'Invoice notification processed successfully' };
    } catch (error) {
      console.error('Error handling invoice notification:', error);
      return { status: 'error', message: `Error processing notification: ${error.message}` };
    }
  }

  private async cancelPendingTransactionsForSamePlan(companyId: string, planId: string, currentOrderId: string): Promise<void> {
    try {
      // Find all pending transactions for the same company and plan, excluding the current one
      const pendingPayments = await this.paymentsRepository.find({
        where: {
          companyId: companyId,
          plan_id: planId,
          status: 'pending',
          order_id: Not(currentOrderId) // TypeORM's Not operator to exclude current order
        }
      });
      
      console.log(`Found ${pendingPayments.length} pending transactions to cancel for company ${companyId}, plan ${planId}`);
      
      // Update all found payments to 'cancelled'
      for (const payment of pendingPayments) {
        payment.status = 'cancelled';
        payment.cancellation_reason = 'Cancelled due to successful payment of same plan';
        await this.paymentsRepository.save(payment);
        console.log(`Cancelled pending transaction ${payment.order_id}`);
        
        // Optionally, cancel the transaction in Midtrans if needed
        try {
          await this.midtransService.cancelTransaction(payment.order_id);
        } catch (error) {
          console.error(`Failed to cancel transaction in payment gateway: ${payment.order_id}`, error);
          // Continue even if Midtrans cancellation fails
        }
      }
    } catch (error) {
      console.error('Error cancelling pending transactions:', error);
    }
  }

}
