import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompaniesModule } from './companies/companies.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { PaymentsModule } from './payments/midtrans.module';
import { SubscriptionsModule } from './subscriptions/subscriptions.module';
import { User } from './users/entities/user.entity';
import { Company } from './users/entities/company.entity';
import { TokenUsage } from './users/entities/token-usage.entity';
import { Subscription } from './subscriptions/entities/subscription.entity';
import { CompanySubscription } from './subscriptions/entities/company-subscription.entity';
import { Payment } from './payments/entities/payment.entity';
import { ProfileModule } from './profile/profile.module';
import { Member } from './users/entities/member.entity';

const synchronize = process.env.NODE_ENV !== 'production';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 5432),
        username: configService.get('DB_USERNAME', 'postgres'),
        password: configService.get('DB_PASSWORD', 'postgres'),
        database: configService.get('DB_DATABASE', 'agentq'),
        entities: [User, Company, TokenUsage, Subscription, CompanySubscription, Payment, Member],
        synchronize: synchronize,
      }),
      inject: [ConfigService],
    }),
    CompaniesModule,
    AuthModule,
    UsersModule,
    SubscriptionsModule,
    PaymentsModule,
    ProfileModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
