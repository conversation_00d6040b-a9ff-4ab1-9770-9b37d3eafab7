import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import SignupView from '../views/SignupView.vue'
import AuthCallback from '../views/AuthCallback.vue'
import HomeView from '../views/HomeView.vue'
import ConfirmationEmailView from '../views/ConfirmationEmailView.vue'
import ConfirmEmailMemberInvitationView from '../views/ConfirmEmailMemberInvitationView.vue'
import ForgotPasswordView from '../views/ForgotPasswordView.vue'
import ResetPasswordView from '../views/ResetPasswordView.vue'
import SettingsView from '../views/SettingsView.vue'
import { useAuthStore } from '../stores/auth'

const router = createRouter({
  history: createWebHistory((import.meta as any).env.BASE_URL),
  routes: [
    {
      path: '/dashboard',
      name: 'home',
      component: HomeView,
      meta: { requiresAuth: true }
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView
    },
    {
      path: '/signup',
      name: 'signup',
      component: SignupView
    },
    {
      path: '/auth/callback',
      name: 'auth-callback',
      component: AuthCallback
    },
    {
      path: '/confirm-email',
      name: 'confirm-email',
      component: ConfirmationEmailView
    },
    {
      path: '/confirm-email-member-invitation',
      name: 'confirm-email-member-invitation',
      component: ConfirmEmailMemberInvitationView
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: ForgotPasswordView
    },
    {
      path: '/reset-password',
      name: 'reset-password',
      component: ResetPasswordView
    },
    {
      path: '/payment/finish',
      name: 'PaymentFinish',
      component: () => import('../views/PaymentFinishView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/payment/resume',
      name: 'PaymentResume',
      component: () => import('../views/PaymentResumeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/invoice/:id',
      name: 'InvoiceDetails',
      component: () => import('../views/InvoiceDetailsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/settings',
      name: 'settings',
      component: SettingsView,
      meta: { requiresAuth: true }
    }
  ]
})

// Navigation guard
router.beforeEach((to, _,next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.token) {
    next('/login')
  } else {
    next()
  }
})

export default router
