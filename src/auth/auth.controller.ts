import { Controller, Get, Req, Res, UseGuards, Post, Body, Query, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { AuthService } from './auth.service';
import { ConfigService } from '@nestjs/config';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { UsersService } from './users.service';
import { ResendService } from '../mailer/resend.service';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
    private readonly resendService: ResendService,
  ) {}

  @Post('signup')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  async signup(@Body() signupData: {
    name: string;
    email: string;
    company?: string;
    password: string;
  }) {
    return this.authService.signup(signupData);
  }

  @Post('login')
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({ 
    status: 200, 
    description: 'Login successful',
    schema: {
      example: {
        access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        refresh_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        expires_in: 3600,
        user: {
          id: '123e4567-e89b-12d3-a456-426614174000',
          email: '<EMAIL>',
          name: 'John Doe',
          role: 'admin',
          company: 'Acme Inc',
          avatarUrl: 'https://example.com/avatar.jpg'
        }
      }
    }
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Invalid credentials',
    schema: {
      example: {
        statusCode: 401,
        message: 'Invalid credentials',
        error: 'Unauthorized'
      }
    }
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          example: '<EMAIL>',
          description: 'User email address'
        },
        password: {
          type: 'string',
          example: 'password123',
          description: 'User password'
        }
      },
      required: ['email', 'password']
    },
    examples: {
      validCredentials: {
        summary: 'Valid credentials example',
        value: { email: '<EMAIL>', password: 'password123' }
      },
      invalidEmail: {
        summary: 'Invalid email format',
        value: { email: 'invalid-email', password: 'password123' }
      }
    }
  })
  async login(@Body() loginData: { email: string; password: string }) {
    return this.authService.login(loginData.email, loginData.password);
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  async forgotPassword(@Body() data: { email: string }) {
    return this.authService.forgotPassword(data.email);
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 200, description: 'Password reset successful' })
  async resetPassword(@Body() data: { token: string; password: string }) {
    return this.authService.resetPassword(data.token, data.password);
  }

  @Get('confirm')
  @ApiOperation({ summary: 'Confirm email address' })
  @ApiResponse({ status: 200, description: 'Email confirmed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid or expired token' })
  async confirmEmail(@Query('token') token: string) {
    if (!token) {
      throw new BadRequestException('Confirmation token is required');
    }
    return this.authService.confirmEmail(token);
  }

  @Get('github')
  @UseGuards(AuthGuard('github'))
  @ApiOperation({ summary: 'GitHub OAuth login' })
  async githubAuth() {
    // Guard redirects to GitHub
  }

  @Get('github/callback')
  @UseGuards(AuthGuard('github'))
  @ApiOperation({ summary: 'GitHub OAuth callback' })
  @ApiResponse({ status: 200, description: 'Successfully authenticated' })
  async githubAuthCallback(@Req() req: any, @Res() res: Response) {
    try {
      const user = await this.authService.validateGithubUser(req.user);
      const { access_token, user: userData } = await this.authService.login(user.email);
      const frontendUrl = this.configService.get('FRONTEND_URL');
      res.redirect(`${frontendUrl}/auth/callback?token=${access_token}&user=${JSON.stringify(userData)}`);
    } catch (error) {
      console.error('GitHub callback error:', error);
      const frontendUrl = this.configService.get('FRONTEND_URL');
      res.redirect(`${frontendUrl}/login?error=github_auth_failed`);
    }
  }

  @Get('google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Google OAuth login' })
  async googleAuth() {
    // Guard redirects to Google
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Google OAuth callback' })
  @ApiResponse({ status: 200, description: 'Successfully authenticated' })
  async googleAuthCallback(@Req() req: any, @Res() res: Response) {
    try {
      const user = await this.authService.validateGoogleUser(req.user);
      const { access_token, user: userData } = await this.authService.login(user.email);
      const frontendUrl = this.configService.get('FRONTEND_URL');
      res.redirect(`${frontendUrl}/auth/callback?token=${access_token}&user=${JSON.stringify(userData)}`);
    } catch (error) {
      console.error('Google callback error:', error);
      const frontendUrl = this.configService.get('FRONTEND_URL');
      res.redirect(`${frontendUrl}/login?error=google_auth_failed`);
    }
  }

  @Post('refresh')
  @ApiOperation({ summary: 'Refresh access token using refresh token' })
  @ApiResponse({ status: 200, description: 'New access token' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    try {
      const { refresh_token } = refreshTokenDto;
      
      // Validate refresh token
      const decoded = await this.authService.verifyRefreshToken(refresh_token);
      
      if (!decoded || !decoded.sub) {
        throw new UnauthorizedException('Invalid refresh token');
      }
      
      // Generate new access token
      const user = await this.usersService.findOne(decoded.sub);
      
      if (!user) {
        throw new UnauthorizedException('User not found');
      }
      
      const tokens = await this.authService.generateTokens(user);
      
      return {
        access_token: tokens.accessToken,
        refresh_token: tokens.refreshToken,
        expires_in: 3600 // 1 hour
      };
    } catch (error) {
      console.error('Token refresh error:', error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  @Post('test-resend')
  @ApiOperation({ summary: 'Test Resend email sending (development only)' })
  async testResendEmail(@Body() data: { email: string }) {
    if (process.env.NODE_ENV === 'production') {
      throw new BadRequestException('This endpoint is only available in development mode');
    }
    
    try {
      console.log('Testing Resend email sending to:', data.email);
      
      const result = await this.resendService.sendEmail({
        to: data.email,
        subject: 'Test Email from Resend',
        html: '<h1>This is a test email from Resend</h1><p>If you received this, the Resend integration is working correctly!</p>',
      });
      
      return {
        success: true,
        message: 'Test email sent successfully with Resend',
        id: result?.data?.id, // Access id through data property
      };
    } catch (error) {
      console.error('Test Resend email error:', error);
      
      return {
        success: false,
        error: error.message,
      };
    }
  }
}
