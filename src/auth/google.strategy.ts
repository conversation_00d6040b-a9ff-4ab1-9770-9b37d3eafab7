import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      clientID: configService.get('GOOGLE_CLIENT_ID'),
      clientSecret: configService.get('GOOGLE_CLIENT_SECRET'),
      callbackURL: `${configService.get('GOOGLE_CALLBACK_URL')}`,
      scope: ['email', 'profile'],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any) {
    try {
      if (!profile || !profile.emails || !profile.emails.length) {
        throw new Error('Invalid Google profile data');
      }

      return {
        id: profile.id,
        email: profile.emails[0].value,
        displayName: profile.displayName || profile.name?.givenName,
        photos: profile.photos || [],
        emails: profile.emails,
      };
    } catch (error) {
      console.error('Google profile validation error:', error);
      throw error;
    }
  }
}