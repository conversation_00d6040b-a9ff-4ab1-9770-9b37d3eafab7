import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-github2';
import { ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';

@Injectable()
export class GithubStrategy extends PassportStrategy(Strategy, 'github') {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      clientID: configService.get('GITHUB_CLIENT_ID'),
      clientSecret: configService.get('GITHUB_CLIENT_SECRET'),
      callbackURL: `${configService.get('GITHUB_CALLBACK_URL')}`,
      scope: ['user:email'],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any) {
    try {
      if (!profile || !profile.emails || !profile.emails.length) {
        throw new Error('Invalid Github profile data');
      }

      return {
        id: profile.id,
        email: profile.emails[0].value,
        displayName: profile.displayName || profile.name?.givenName,
        photos: profile.photos || [],
        emails: profile.emails,
      };
    } catch (error) {
      console.error('Github profile validation error:', error);
      throw error;
    }
  }
}