import { Injectable, BadRequestException, UnauthorizedException, InternalServerErrorException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserRole } from '../users/entities/user.entity';
import { Company } from '../users/entities/company.entity';
import { SubscriptionsService } from '../subscriptions/subscriptions.service';
import * as bcrypt from 'bcryptjs';
import * as crypto from 'crypto';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import { ResendService } from '../mailer/resend.service';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
    private jwtService: JwtService,
    private mailerService: MailerService,
    private configService: ConfigService,
    private subscriptionsService: SubscriptionsService,
    private resendService: ResendService,
  ) {}

  private generateRandomPassword(length: number = 16): string {
    return crypto.randomBytes(length).toString('hex');
  }

  private generateCompanyToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  private generateToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  async signup(data: {
    name: string;
    email: string;
    company?: string;
    password: string;
  }) {
    try {
      // Check if user exists
      const existingUser = await this.usersRepository.findOne({
        where: { email: data.email },
        select: ['id', 'email', 'password', 'githubId', 'googleId'],
      });

      if (existingUser && existingUser.password) {
        throw new BadRequestException('Email already registered');
      }

      const hashedPassword = await bcrypt.hash(data.password, 10);
      const confirmationToken = this.generateToken();

      // Create user first
      const user = this.usersRepository.create({
        name: data.name,
        email: data.email,
        password: hashedPassword,
        role: UserRole.SUPERADMIN,
        confirmationToken,
        confirmationTokenExpires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        isEmailConfirmed: false,
      });

      const savedUser = await this.usersRepository.save(user);

      // Create company with reference to the user
      const company = this.companiesRepository.create({
        company_name: data.company || 'Default Company',
        token: this.generateCompanyToken(),
        userId: savedUser.id,
        users: [savedUser]
      });

      const savedCompany = await this.companiesRepository.save(company);

      // Assign free subscription to the company
      await this.subscriptionsService.assignFreeSubscriptionToCompany(savedCompany.id);

      // Update user with company reference
      savedUser.company = company;
      await this.usersRepository.save(savedUser);
      
      try {
        await this.sendConfirmationEmail(savedUser);
      } catch (emailError) {
        console.error('Failed to send confirmation email:', emailError);
        // Don't throw here, just log the error
      }

      return { 
        message: 'Registration successful! Please check your email to confirm your account.',
        user: {
          id: savedUser.id,
          email: savedUser.email,
          name: savedUser.name,
        }
      };
    } catch (error) {
      console.error('Signup error:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create account. Please try again.');
    }
  }

  private async sendConfirmationEmail(user: User) {
    try {
      const frontendUrl = this.configService.get('FRONTEND_URL');
      const confirmationUrl = `${frontendUrl}/confirm-email?token=${user.confirmationToken}`;
      
      // Use Resend instead of mailerService
      await this.resendService.sendTemplateEmail({
        to: user.email,
        subject: 'Confirm your email',
        template: 'confirmation',
        data: {
          name: user.name,
          confirmationUrl,
        },
      });
      
      console.log('Confirmation email sent successfully to:', user.email);
    } catch (error) {
      console.error('Failed to send confirmation email:', error);
      throw new InternalServerErrorException('Failed to send confirmation email');
    }
  }

  async confirmEmail(token: string) {
    const user = await this.usersRepository.findOne({
      where: { confirmationToken: token },
      relations: ['company'],
    });

    if (!user) {
      throw new BadRequestException('Invalid confirmation token');
    }

    if (user.confirmationTokenExpires < new Date()) {
      throw new BadRequestException('Confirmation token has expired');
    }

    user.isEmailConfirmed = true;
    user.confirmationToken = null;
    user.confirmationTokenExpires = null;
    await this.usersRepository.save(user);

    // Get the app URL for redirection
    const appUrl = this.configService.get('AGENTQ_APP_URL');
    
    return { 
      message: 'Email confirmed successfully',
      email: user.email,
      redirectUrl: appUrl || null,
      companyId: user.company?.id || null
    };
  }

  async login(email: string, password?: string) {
    const user = await this.usersRepository.findOne({
      where: { email },
      select: ['id', 'email', 'password', 'name', 'isEmailConfirmed', 'avatarUrl', 'githubId', 'googleId', 'role'],
      relations: ['company'],
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // should be superadmin only
    if (user.role !== UserRole.SUPERADMIN) {
      throw new UnauthorizedException('Not registered yet');
    }

    // For social login users (no password check needed)
    if ((user.githubId || user.googleId) && !password) {
      const payload = { sub: user.id, email: user.email, role: user.role };
      return {
        access_token: this.jwtService.sign(payload),
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          company: user.company?.company_name,
          avatarUrl: user.avatarUrl,
        },
      };
    }

    // For regular login
    if (!user.password || !password) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (!user.isEmailConfirmed) {
      throw new UnauthorizedException('Please confirm your email before logging in');
    }

    const payload = { sub: user.id, email: user.email, role: user.role };
    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        company: user.company?.company_name,
        avatarUrl: user.avatarUrl,
      },
    };
  }

  async validateGithubUser(profile: any): Promise<User> {
    try {
      if (!profile || !profile.email || !profile.emails?.[0]?.value) {
        console.error('Invalid Github profile:', profile);
        throw new BadRequestException('Invalid Github profile data');
      }

      const email = profile.emails[0].value;
      const name = profile.displayName || profile.username;
      const githubId = profile.id.toString();
      const avatarUrl = profile._json?.avatar_url;

      let user = await this.usersRepository.findOne({
        where: { email },
        relations: ['company'],
      });

      if (user) {
        // Update GitHub info if not present
        if (!user.githubId) {
          user.githubId = githubId;
          user.avatarUrl = avatarUrl || user.avatarUrl;
          user.name = user.name || name;
          await this.usersRepository.save(user);
        }
        return user;
      }

      // Create new user with a new company
      const company = this.companiesRepository.create({
        company_name: `${email}'s Company`,
        token: this.generateCompanyToken(),
      });

      const savedCompany = await this.companiesRepository.save(company);

      // Create new user
      const newUser = this.usersRepository.create({
        email,
        name,
        githubId,
        avatarUrl,
        role: UserRole.SUPERADMIN,
        isEmailConfirmed: true, // Auto-confirm for social login
        company: savedCompany,
      });

      const savedUser = await this.usersRepository.save(newUser);
      
      // Update company with user reference
      savedCompany.userId = savedUser.id;
      savedCompany.users = [savedUser];
      await this.companiesRepository.save(savedCompany);

      // Assign free subscription to the company
      await this.subscriptionsService.assignFreeSubscriptionToCompany(savedCompany.id);

      return savedUser;
    } catch (error) {
      console.error('GitHub user validation error:', error);
      throw new BadRequestException('Failed to validate GitHub user');
    }
  }

  async validateGoogleUser(profile: any): Promise<User> {
    try {
      if (!profile || !profile.email || !profile.emails?.[0]?.value) {
        console.error('Invalid Google profile:', profile);
        throw new BadRequestException('Invalid Google profile data');
      }

      const email = profile.emails[0].value;
      const name = profile.displayName;
      const googleId = profile.id;
      const avatarUrl = profile.photos?.[0]?.value;

      let user = await this.usersRepository.findOne({
        where: { email },
        relations: ['company'],
      });

      if (user) {
        // Update Google info if not present
        if (!user.googleId) {
          user.googleId = googleId;
          user.avatarUrl = avatarUrl || user.avatarUrl;
          user.name = user.name || name;
          await this.usersRepository.save(user);
        }
        return user;
      }

      // Create new user with a new company
      const company = this.companiesRepository.create({
        company_name: `${email}'s Company`,
        token: this.generateCompanyToken(),
      });

      const savedCompany = await this.companiesRepository.save(company);

      // Create new user
      const newUser = this.usersRepository.create({
        email,
        name,
        googleId,
        avatarUrl,
        role: UserRole.SUPERADMIN,
        isEmailConfirmed: true, // Auto-confirm for social login
        company: savedCompany,
      });

      const savedUser = await this.usersRepository.save(newUser);
      
      // Update company with user reference
      savedCompany.userId = savedUser.id;
      savedCompany.users = [savedUser];
      await this.companiesRepository.save(savedCompany);

      // Assign free subscription to the company
      await this.subscriptionsService.assignFreeSubscriptionToCompany(savedCompany.id);

      return savedUser;
    } catch (error) {
      console.error('Google user validation error:', error);
      throw new BadRequestException('Failed to validate Google user');
    }
  }

  async forgotPassword(email: string) {
    try {
      const user = await this.usersRepository.findOne({ where: { email } });
      if (!user) {
        // Don't reveal if user exists
        return { message: 'If an account exists with this email, you will receive a password reset link.' };
      }

      const resetToken = this.generateToken();
      user.resetPasswordToken = resetToken;
      user.resetPasswordExpires = new Date(Date.now() + 3600000); // 1 hour
      await this.usersRepository.save(user);

      // In development mode, return the token directly without sending email
      if (process.env.NODE_ENV === 'development') {
        console.log('Development mode: Password reset token:', resetToken);
        const frontendUrl = this.configService.get('FRONTEND_URL');
        const resetUrl = `${frontendUrl}/reset-password?token=${resetToken}`;
        console.log('Reset URL:', resetUrl);
        
        return { 
          message: 'If an account exists with this email, you will receive a password reset link.',
          // Only include token in development mode
          ...(process.env.NODE_ENV === 'development' && { 
            devToken: resetToken,
            devResetUrl: resetUrl
          })
        };
      }

      try {
        await this.sendPasswordResetEmail(user);
        return { message: 'If an account exists with this email, you will receive a password reset link.' };
      } catch (error) {
        console.error('Failed to send reset password email:', error);
        
        // Don't expose error details to client
        return { message: 'If an account exists with this email, you will receive a password reset link.' };
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      throw new InternalServerErrorException('An error occurred while processing your request');
    }
  }

  async resetPassword(token: string, newPassword: string) {
    const user = await this.usersRepository.findOne({
      where: { resetPasswordToken: token }
    });

    if (!user) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    if (!user.resetPasswordExpires || user.resetPasswordExpires < new Date()) {
      throw new BadRequestException('Reset token has expired');
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedPassword;
    user.resetPasswordToken = null;
    user.resetPasswordExpires = null;
    await this.usersRepository.save(user);

    return { message: 'Password has been reset successfully' };
  }

  async verifyRefreshToken(refreshToken: string): Promise<any> {
    try {
      // Verify the refresh token using the JWT service
      const decoded = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET', this.configService.get('JWT_SECRET')),
      });
      
      return decoded;
    } catch (error) {
      console.error('Refresh token verification error:', error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async generateTokens(user: User) {
    const payload = { sub: user.id, email: user.email, role: user.role };
    
    // Generate access token
    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '1h', // Access token expires in 1 hour
    });
    
    // Generate refresh token with longer expiry
    const refreshToken = this.jwtService.sign(payload, {
      expiresIn: '7d', // Refresh token expires in 7 days
      secret: this.configService.get('JWT_REFRESH_SECRET', this.configService.get('JWT_SECRET')),
    });
    
    return {
      accessToken,
      refreshToken,
    };
  }

  private async sendPasswordResetEmail(user: User) {
    try {
      const frontendUrl = this.configService.get('FRONTEND_URL');
      if (!frontendUrl) {
        console.warn('FRONTEND_URL not configured in environment variables');
      }
      
      const resetUrl = `${frontendUrl}/reset-password?token=${user.resetPasswordToken}`;
      
      console.log('Sending password reset email to:', user.email);
      
      // Use Resend instead of mailerService
      const result = await this.resendService.sendTemplateEmail({
        to: user.email,
        subject: 'Reset your password',
        template: 'reset-password',
        data: {
          name: user.name,
          resetUrl,
        },
      });
      
      console.log('Password reset email sent successfully:', result?.data?.id); // Access id through data property
      return true;
    } catch (error) {
      console.error('Failed to send password reset email:', error);
      
      // In development, don't throw - just log the error
      if (process.env.NODE_ENV === 'development') {
        return false;
      }
      
      throw new InternalServerErrorException('Failed to send password reset email');
    }
  }
}
