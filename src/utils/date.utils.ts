/**
 * Calculate subscription end date based on duration in months
 * @param duration Number of months for subscription
 * @param startDate Optional start date (defaults to now)
 * @returns Date object representing the end date
 */
export function calculateSubscriptionEndDate(duration: number, startDate: Date = new Date()): Date {
  const endDate = new Date(startDate);
  endDate.setMonth(endDate.getMonth() + duration);
  return endDate;
}