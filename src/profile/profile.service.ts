import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Company } from '../users/entities/company.entity';

@Injectable()
export class ProfileService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
  ) {}

  async getProfile(userId: string) {
    const user = await this.usersRepository.findOne({
      where: { id: userId },
      relations: ['company', 'company.subscriptions'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Transform the data to include subscription information
    let subscription = null;
    if (user.company && user.company.subscriptions && user.company.subscriptions.length > 0) {
      // Get the active subscription (most recent one)
      const activeSubscription = user.company.subscriptions
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];
      
      subscription = {
        id: activeSubscription.id,
        name: activeSubscription.company.company_name,
        isEnterprise: activeSubscription.subscription.name === 'Enterprise',
      };
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      company: user.company ? {
        id: user.company.id,
        name: user.company.company_name,
        subscription,
        users: await this.getCompanyUsers(user.company.id),
      } : null,
    };
  }

  async getCompanyUsers(companyId: string) {
    const company = await this.companiesRepository.findOne({
      where: { id: companyId },
      relations: ['users'],
    });

    if (!company) {
      return [];
    }

    return company.users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
    }));
  }

  async updateCompanyName(userId: string, updateData: { name: string }) {
    const user = await this.usersRepository.findOne({
      where: { id: userId },
      relations: ['company'],
    });

    if (!user || !user.company) {
      throw new NotFoundException('User or company not found');
    }

    // Update company name using raw SQL query
    await this.companiesRepository.query(
      `UPDATE companies SET company_name = $1 WHERE id = $2`,
      [updateData.name, user.company.id]
    );

    // Fetch the updated company
    const updatedCompany = await this.companiesRepository.findOne({
      where: { id: user.company.id }
    });

    return {
      success: true,
      message: 'Company name updated successfully',
      company: {
        id: updatedCompany.id,
        name: updatedCompany.company_name,
      },
    };
  }
}
