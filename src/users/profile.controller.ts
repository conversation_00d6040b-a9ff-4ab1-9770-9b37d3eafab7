import { Controller, Get, Put, Post, Body, UseGuards, Req } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProfileService } from './profile.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { ValidateApiKeyDto } from './dto/validate-api-key.dto';
import { CreateTokenUsageDto } from './dto/create-token-usage.dto';

@ApiTags('profile')
@Controller('profile')
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  @Get()
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({ status: 200, description: 'Returns user profile data' })
  async getProfile(@Req() req: any) {
    return this.profileService.getProfile(req.user.id);
  }

  @Post('profile-by-key')
  @ApiOperation({ summary: 'Get company profile by API key' })
  @ApiResponse({ status: 200, description: 'Returns company profile data' })
  @ApiResponse({ status: 401, description: 'Invalid API key' })
  @ApiResponse({ status: 404, description: 'No subscription found' })
  async getProfileByApiKey(@Body() apiKeyDto: ValidateApiKeyDto) {
    return this.profileService.getProfileByApiKey(apiKeyDto.apiKey);
  }

  @Get('usage')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get token usage data' })
  @ApiResponse({ status: 200, description: 'Returns token usage data' })
  async getUsage(@Req() req: any) {
    return this.profileService.getUsage(req.user.id);
  }

  @Put()
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  async updateProfile(@Req() req: any, @Body() updateProfileDto: UpdateProfileDto) {
    return this.profileService.updateProfile(req.user.id, updateProfileDto);
  }

  @Post('validate-api-key')
  @ApiOperation({ summary: 'Validate API key' })
  @ApiResponse({ status: 200, description: 'API key is valid' })
  @ApiResponse({ status: 401, description: 'Invalid API key' })
  async validateApiKey(@Body() validateApiKeyDto: ValidateApiKeyDto) {
    return this.profileService.validateApiKey(validateApiKeyDto.apiKey);
  }

  @Post('usage-token')
  @ApiOperation({ summary: 'Record token usage' })
  @ApiResponse({ status: 201, description: 'Token usage recorded successfully' })
  async recordTokenUsage(@Body() createTokenUsageDto: CreateTokenUsageDto) {
    return this.profileService.recordTokenUsage(createTokenUsageDto);
  }
}