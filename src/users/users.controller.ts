import { Controller, Get, Post, Body, UseGuards, Req, Delete, Param, NotFoundException, ForbiddenException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { UsersService } from './user.service';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post('update-company')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update company name' })
  async updateCompanyName(
    @Body() body: { userId: string; companyName: string },
    @Req() req: any
  ) {
    // Check if the user is updating their own company
    if (req.user.id !== body.userId) {
      throw new Error('You can only update your own company');
    }
    
    return this.usersService.updateCompanyName(body.userId, body.companyName);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a user' })
  async deleteUser(@Param('id') id: string, @Req() req: any) {
    console.log(`Delete request for user ${id} from user ${req.user.id}`);
    
    // Simple implementation for testing
    return this.usersService.deleteUser(id);
  }

  @Get('test')
  @ApiOperation({ summary: 'Test endpoint' })
  testEndpoint() {
    return { message: 'Users controller test endpoint working' };
  }
}
