import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersService } from './user.service';
import { UsersController } from './user.controller';
import { User } from './entities/user.entity';
import { Company } from './entities/company.entity';
import { TokenUsage } from './entities/token-usage.entity';
import { ProfileService } from './profile.service';
import { ProfileController } from './profile.controller';
import { SubscriptionsModule } from '../subscriptions/subscriptions.module';
import { CompanySubscription } from '../subscriptions/entities/company-subscription.entity';
import { Payment } from '../payments/entities/payment.entity';
import { Member } from './entities/member.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Company, TokenUsage, CompanySubscription, Payment, Member]),
    SubscriptionsModule
  ],
  controllers: [UsersController, ProfileController],
  providers: [UsersService, ProfileService],
  exports: [UsersService]
})
export class UsersModule {}
