import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  email: string;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  password: string;

  @Column({ nullable: true })
  company: string;

  @Column({ nullable: true })
  githubId: string;

  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;
}