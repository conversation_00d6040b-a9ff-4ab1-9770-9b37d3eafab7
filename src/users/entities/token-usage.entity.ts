import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne } from 'typeorm';
import { Company } from './company.entity';
import { TokenType } from '../dto/create-token-usage.dto';

@Entity('token_usage')
export class TokenUsage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Company)
  company: Company;

  @Column()
  companyId: string;

  @Column({
    type: 'enum',
    enum: TokenType
  })
  tokenType: TokenType;

  @Column()
  tokenUsed: number;

  @CreateDateColumn()
  timestamp: Date;
}