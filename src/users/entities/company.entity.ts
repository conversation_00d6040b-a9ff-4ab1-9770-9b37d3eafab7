import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { User } from './user.entity';
import { CompanySubscription } from '../../subscriptions/entities/company-subscription.entity';
import { Member } from './member.entity';

@Entity('companies')
export class Company {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  company_name: string;

  @Column({ unique: true, nullable: true })
  token: string;

  @Column({ nullable: true })
  userId: string;

  @OneToMany(() => User, user => user.company)
  users: User[];

  @OneToMany(() => CompanySubscription, companySubscription => companySubscription.company)
  subscriptions: CompanySubscription[];

  @OneToMany(() => Member, member => member.company)
  members: Member[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
