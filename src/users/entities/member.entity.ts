import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Company } from './company.entity';
import { User } from './user.entity';

export enum MemberRole {
  ADMIN = 'admin',
  USER = 'user'
}

@Entity('members')
export class Member {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  name: string;

  @Column()
  email: string;

  @Column({ nullable: true, select: false }) // select: false for security
  originalPassword: string;

  @Column({
    type: 'enum',
    enum: MemberRole,
    default: MemberRole.USER
  })
  role: MemberRole;

  @Column()
  companyId: string;

  @ManyToOne(() => Company, company => company.members)
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @Column({ nullable: true })
  userId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ default: false })
  isEmailConfirmed: boolean;

  @Column({ nullable: true })
  confirmationToken: string;

  @Column({ nullable: true })
  confirmationTokenExpires: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
