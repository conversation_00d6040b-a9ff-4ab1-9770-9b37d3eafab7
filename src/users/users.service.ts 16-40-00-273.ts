import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { Company } from './entities/company.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
  ) {}

  async updateCompanyName(userId: string, companyName: string) {
    const user = await this.usersRepository.findOne({
      where: { id: userId },
      relations: ['company'],
    });

    if (!user || !user.company) {
      throw new NotFoundException('User or company not found');
    }

    // Update company name
    user.company.company_name = companyName;
    await this.companiesRepository.save(user.company);

    return {
      success: true,
      message: 'Company name updated successfully',
      company: {
        id: user.company.id,
        name: user.company.company_name,
      },
    };
  }
}