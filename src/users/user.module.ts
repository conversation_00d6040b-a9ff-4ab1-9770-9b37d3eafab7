import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Company } from './entities/company.entity';
import { TokenUsage } from './entities/token-usage.entity';
import { CompanySubscription } from '../subscriptions/entities/company-subscription.entity';
import { Payment } from '../payments/entities/payment.entity';
import { ProfileController } from './profile.controller';
import { ProfileService } from './profile.service';
import { SubscriptionsModule } from '../subscriptions/subscriptions.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Company, TokenUsage, CompanySubscription, Payment]),
    SubscriptionsModule
  ],
  controllers: [ProfileController],
  providers: [ProfileService],
  exports: [ProfileService],
})
export class UsersModule {}
