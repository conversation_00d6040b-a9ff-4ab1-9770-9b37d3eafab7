import { <PERSON><PERSON><PERSON>, <PERSON>Not<PERSON>mpty, <PERSON><PERSON>num, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum TokenType {
  EMBEDDING_INPUT = 'embedding_input',
  COMPLETION_INPUT = 'completion_input',
  COMPLETION_OUTPUT = 'completion_output'
}

export class CreateTokenUsageDto {
  @ApiProperty({ enum: TokenType })
  @IsEnum(TokenType)
  @IsNotEmpty()
  tokenType: TokenType;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  tokenUsed: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  token: string;
}