import { Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { Company } from './entities/company.entity';
import { TokenUsage } from './entities/token-usage.entity';
import { CompanySubscription } from '../subscriptions/entities/company-subscription.entity';
import { Payment } from '../payments/entities/payment.entity';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { CreateTokenUsageDto } from './dto/create-token-usage.dto';
import { SubscriptionsService } from '../subscriptions/subscriptions.service';

@Injectable()
export class ProfileService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(TokenUsage)
    private readonly tokenUsageRepository: Repository<TokenUsage>,
    @InjectRepository(CompanySubscription)
    private readonly companySubscriptionRepository: Repository<CompanySubscription>,
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
    private readonly subscriptionsService: SubscriptionsService,
  ) {}

  async getProfile(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['company'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get all company subscriptions to find the active Enterprise subscription
    const companySubscriptions = await this.companySubscriptionRepository.find({
      where: { companyId: user.company.id },
      relations: ['subscription'],
      order: { created_at: 'DESC' }
    });

    // First look for an active Enterprise subscription
    const now = new Date();
    
    // Check for Enterprise subscriptions with valid payments
    const latestEnterprisePaymentForSubscription = await this.paymentRepository.findOne({
      where: {
        companyId: user.company.id,
        plan_id: 'enterprise',
        status: 'settlement',
      },
      order: {
        created_at: 'DESC',
      },
    });
    
    // If we have a valid enterprise payment, use the enterprise subscription
    let companySubscription;
    if (latestEnterprisePaymentForSubscription && 
        latestEnterprisePaymentForSubscription.valid_until && 
        new Date(latestEnterprisePaymentForSubscription.valid_until) > now) {
      
      // Find the enterprise subscription
      companySubscription = companySubscriptions.find(sub => 
        sub.subscription.name === 'Enterprise'
      );
    }
    
    // If no active Enterprise subscription found, fall back to the Free subscription
    if (!companySubscription) {
      companySubscription = await this.subscriptionsService.getCompanySubscription(user.company.id);
    }
    
    if (!companySubscription) {
      throw new NotFoundException('No active subscription found');
    }

    // Get the latest successful enterprise payment to get the actual subscription end date
    const latestEnterprisePayment = await this.paymentRepository.findOne({
      where: {
        companyId: user.company.id,
        plan_id: 'enterprise',
        status: 'settlement',
      },
      order: {
        created_at: 'DESC',
      },
    });

    // Calculate total token usage
    const totalTokenUsage = await this.tokenUsageRepository
      .createQueryBuilder('token_usage')
      .where('token_usage.companyId = :companyId', { companyId: user.company.id })
      .select('SUM(token_usage.tokenUsed)', 'total')
      .getRawOne();

    const totalUsed = parseInt(totalTokenUsage?.total || '0');
    const remainingTokens = companySubscription.remaining_tokens;

    // Calculate days until reset
    const currentDate = new Date();
    const nextResetDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
    const daysUntilReset = Math.ceil(
      (nextResetDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Use the valid_until from the payment if available, otherwise fall back to the subscription end_date
    const subscriptionEndDate = latestEnterprisePayment?.valid_until || companySubscription.end_date;

    // Add debug logging
    console.log('Subscription details for user profile:');
    console.log(`- Company ID: ${user.company.id}`);
    console.log(`- Subscription name: ${companySubscription.subscription.name}`);
    console.log(`- Subscription end date from DB: ${companySubscription.end_date}`);
    console.log(`- Latest payment valid_until: ${latestEnterprisePayment?.valid_until}`);
    console.log(`- Using end date: ${subscriptionEndDate}`);

    return {
      company: {
        id: user.company.id,
        name: user.company?.company_name,
        token: user.company?.token,
        userId: user.company?.userId,
        users: await this.userRepository.find({
          where: { company: { id: user.company.id } },
          select: ['id', 'name', 'email', 'role']
        }),
        subscription: {
          name: companySubscription.subscription.name,
          tokenLimit: companySubscription.subscription.token_limit,
          tokensUsed: totalUsed,
          remainingTokens: Math.max(0, remainingTokens),
          startDate: companySubscription.start_date,
          endDate: subscriptionEndDate,
          price: companySubscription.subscription.price,
          billingCycle: companySubscription.subscription.billing_cycle,
          daysUntilReset: daysUntilReset,
          isEnterprise: companySubscription.subscription.name.toLowerCase() === 'enterprise'
        }
      }
    };
  }

  async getProfileByApiKey(apiKey: string) {
    // First find the company by API key
    const company = await this.companyRepository.findOne({
      where: { token: apiKey },
    });

    if (!company) {
      throw new UnauthorizedException('Invalid API key');
    }

    // Get all company subscriptions to find the active Enterprise subscription
    const companySubscriptions = await this.companySubscriptionRepository.find({
      where: { companyId: company.id },
      relations: ['subscription'],
      order: { created_at: 'DESC' }
    });

    // First look for an active Enterprise subscription
    const now = new Date();
    const activeEnterpriseSubscription = companySubscriptions.find(sub => 
      sub.subscription.name === 'Enterprise' && 
      (!sub.end_date || new Date(sub.end_date) > now)
    );
    
    // If no active Enterprise subscription, fall back to the Free subscription
    const companySubscription = activeEnterpriseSubscription || 
      await this.subscriptionsService.getCompanySubscription(company.id);

    if (!companySubscription) {
      throw new NotFoundException('Subscription not found');
    }

    // Get the latest successful enterprise payment to get the actual subscription end date
    const latestEnterprisePayment = await this.paymentRepository.findOne({
      where: {
        companyId: company.id,
        plan_id: 'enterprise',
        status: 'settlement',
      },
      order: {
        created_at: 'DESC',
      },
    });

    // Calculate total token usage
    const totalTokenUsage = await this.tokenUsageRepository
      .createQueryBuilder('token_usage')
      .where('token_usage.companyId = :companyId', { companyId: company.id })
      .select('SUM(token_usage.tokenUsed)', 'total')
      .getRawOne();

    const totalUsed = parseInt(totalTokenUsage?.total || '0');
    const remainingTokens = companySubscription.remaining_tokens;

    // Calculate days until reset
    const currentDate = new Date();
    const nextResetDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
    const daysUntilReset = Math.ceil(
      (nextResetDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Use the valid_until from the payment if available, otherwise fall back to the subscription end_date
    const subscriptionEndDate = latestEnterprisePayment?.valid_until || companySubscription.end_date;

    return {
      company: {
        name: company.company_name,
        token: company.token,
        subscription: {
          name: companySubscription.subscription.name,
          tokenLimit: companySubscription.subscription.token_limit,
          tokensUsed: totalUsed,
          remainingTokens: Math.max(0, remainingTokens),
          startDate: companySubscription.start_date,
          endDate: subscriptionEndDate,
          price: companySubscription.subscription.price,
          billingCycle: companySubscription.subscription.billing_cycle,
          daysUntilReset: daysUntilReset,
        }
      }
    };
  }  

  async getUsage(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['company'],
    });

    if (!user || !user.company) {
      throw new NotFoundException('User or company not found');
    }

    const usage = await this.tokenUsageRepository.find({
      where: { companyId: user.company.id },
      order: { timestamp: 'DESC' },
    });

    return usage;
  }

  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Update only allowed fields
    if (updateProfileDto.name) {
      user.name = updateProfileDto.name;
    }
    if (updateProfileDto.avatarUrl) {
      user.avatarUrl = updateProfileDto.avatarUrl;
    }

    await this.userRepository.save(user);

    return {
      message: 'Profile updated successfully',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatarUrl: user.avatarUrl,
      },
    };
  }

  async validateApiKey(apiKey: string) {
    const company = await this.companyRepository.findOne({
      where: { token: apiKey },
    });

    if (!company) {
      throw new UnauthorizedException('Invalid API key');
    }

    // Get current subscription and remaining tokens
    const companySubscription = await this.subscriptionsService.getCompanySubscription(company.id);

    if (!companySubscription) {
      throw new NotFoundException('No active subscription found');
    }

    const status = companySubscription.remaining_tokens > 0 ? 'active' : 'overlimit';

    return {
      valid: true,
      company: {
        name: company.company_name,
        subscription: {
          name: companySubscription.subscription.name,
          remainingTokens: companySubscription.remaining_tokens,
          status
        }
      }
    };
  }

  async recordTokenUsage(createTokenUsageDto: CreateTokenUsageDto) {
    // Find company by token
    const company = await this.companyRepository.findOne({
      where: { token: createTokenUsageDto.token }
    });

    if (!company) {
      throw new UnauthorizedException('Invalid API token');
    }

    // Get current company subscription
    const companySubscription = await this.companySubscriptionRepository.findOne({
      where: { companyId: company.id },
      relations: ['subscription'],
      order: { created_at: 'DESC' }
    });

    if (!companySubscription) {
      throw new NotFoundException('No active subscription found');
    }

    // Check if there are enough tokens
    // if (companySubscription.remaining_tokens < createTokenUsageDto.tokenUsed) {
    //   throw new UnauthorizedException('Insufficient tokens');
    // }

    // Create token usage record
    const tokenUsage = this.tokenUsageRepository.create({
      tokenType: createTokenUsageDto.tokenType,
      tokenUsed: createTokenUsageDto.tokenUsed,
      company,
      companyId: company.id,
    });

    // Update remaining tokens
    companySubscription.remaining_tokens -= createTokenUsageDto.tokenUsed;
    await this.companySubscriptionRepository.save(companySubscription);

    await this.tokenUsageRepository.save(tokenUsage);

    return {
      message: 'Token usage recorded successfully',
      tokenUsage: {
        id: tokenUsage.id,
        tokenType: tokenUsage.tokenType,
        tokenUsed: tokenUsage.tokenUsed,
        timestamp: tokenUsage.timestamp,
        remainingTokens: companySubscription.remaining_tokens
      },
    };
  }
}
