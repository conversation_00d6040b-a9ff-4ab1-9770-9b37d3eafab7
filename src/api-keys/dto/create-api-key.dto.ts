import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateApiKeyDto {
  @ApiProperty({
    description: 'The provider for this API key (e.g., agentq, gemini)',
    example: 'agentq'
  })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({
    description: 'The API key value',
    example: 'sk-1234567890abcdef'
  })
  @IsString()
  @IsNotEmpty()
  apiKey: string;
}