import { IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateApiKeyDto {
  @ApiPropertyOptional({
    description: 'The provider for this API key (e.g., agentq, gemini)',
    example: 'agentq'
  })
  @IsString()
  provider?: string;

  @ApiPropertyOptional({
    description: 'The API key value',
    example: 'sk-1234567890abcdef'
  })
  @IsString()
  apiKey?: string;
}