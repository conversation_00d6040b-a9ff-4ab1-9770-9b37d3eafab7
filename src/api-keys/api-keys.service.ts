import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApiKey } from './api-key.entity';
import { CreateApiKeyDto } from './dto/create-api-key.dto';
import { UpdateApiKeyDto } from './dto/update-api-key.dto';

@Injectable()
export class ApiKeysService {
  constructor(
    @InjectRepository(ApiKey)
    private apiKeysRepository: Repository<ApiKey>,
  ) {}

  async create(userId: string, createApiKeyDto: CreateApiKeyDto): Promise<ApiKey> {
    const apiKey = this.apiKeysRepository.create({
      ...createApiKeyDto,
      userId,
    });
    return this.apiKeysRepository.save(apiKey);
  }

  async findAll(userId: string): Promise<ApiKey[]> {
    return this.apiKeysRepository.find({ where: { userId } });
  }

  async findOne(id: string, userId: string): Promise<ApiKey> {
    const apiKey = await this.apiKeysRepository.findOne({ 
      where: { id, userId } 
    });
    
    if (!apiKey) {
      throw new NotFoundException('API key not found');
    }
    
    return apiKey;
  }

  async update(id: string, userId: string, updateApiKeyDto: UpdateApiKeyDto): Promise<ApiKey> {
    const apiKey = await this.findOne(id, userId);
    
    Object.assign(apiKey, updateApiKeyDto);
    return this.apiKeysRepository.save(apiKey);
  }

  async remove(id: string, userId: string): Promise<void> {
    const apiKey = await this.findOne(id, userId);
    await this.apiKeysRepository.remove(apiKey);
  }
}