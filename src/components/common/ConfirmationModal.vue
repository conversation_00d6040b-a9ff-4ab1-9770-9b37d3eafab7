<script setup lang="ts">
defineProps<{
  isOpen: boolean;
  title: string;
  message: string;
}>();

const emit = defineEmits<{
  'confirm': [];
  'cancel': [];
}>();
</script>

<template>
  <div v-if="isOpen" class="modal-overlay">
    <div class="modal-content">
      <h3>{{ title }}</h3>
      <p>{{ message }}</p>
      <div class="modal-actions">
        <button class="cancel-button" @click="emit('cancel')">Cancel</button>
        <button class="confirm-button" @click="emit('confirm')">Confirm</button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin-bottom: 12px;
    font-size: 20px;
    color: #374151;
  }
  
  p {
    margin-bottom: 24px;
    color: #6b7280;
  }
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  
  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    
    &:hover {
      background-color: #e5e7eb;
    }
  }
  
  .confirm-button {
    background-color: #dc2626;
    color: white;
    border: none;
    
    &:hover {
      background-color: #b91c1c;
    }
  }
}
</style>