<template>
    <transition name="modal">
      <div class="modal-mask" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <h3>Upgrade Your Plan</h3>
            <button class="modal-close" @click="closeModal">
              <span class="material-icons">close</span>
            </button>
          </div>

          <div class="modal-body">
            <!-- Package Plans -->
            <div class="plan-options">
              <div
                v-for="plan in plans"
                :key="plan.id"
                class="plan-card"
                :class="{
                  'selected': selectedPlan?.id === plan.id,
                  'disabled': plan.disabled || (props.currentSubscription.name.toLowerCase() === 'enterprise' && plan.id === 'free'),
                  'free-plan': plan.id === 'free'
                }"
                @click="plan.id !== 'free' ? handlePlanSelect(plan) : null"
              >
              <div class="plan-header">
                <h4>{{ plan.name }}</h4>
                <span class="plan-price">{{ plan.price }}</span>
                <span v-if="plan.id === props.currentSubscription.name.toLowerCase()" class="current-plan-badge">Current Plan</span>
              </div>
                <ul class="plan-features">
                  <li v-for="feature in plan.features" :key="feature">
                    <span class="material-icons">check_circle</span>
                    {{ feature }}
                  </li>
                </ul>
                <!-- Only show duration selector when this specific plan is selected AND it's the enterprise plan -->
                <div v-if="selectedPlan?.id === plan.id && plan.id === 'enterprise'" class="subscription-duration mt-4 pt-4 border-t border-dashed border-gray-200">
                  <div class="duration-header mb-3">Select subscription duration:</div>
                  <div class="duration-options">
                    <div 
                      v-for="duration in [1, 12, 24, 36]" 
                      :key="duration"
                      class="duration-option"
                      :class="{ 'selected': subscriptionDuration === duration }"
                      @click.stop="subscriptionDuration = duration"
                    >
                      {{ duration }} {{ duration === 1 ? 'month' : 'months' }}
                    </div>
                  </div>
                  <div class="duration-summary mt-4">
                    <div class="summary-row">
                      <span>Duration:</span>
                      <span>{{ subscriptionDuration }} {{ subscriptionDuration === 1 ? 'month' : 'months' }}</span>
                    </div>
                    <div class="summary-row">
                      <span>Monthly price:</span>
                      <span>Rp 3,500,000</span>
                    </div>
                    <div class="summary-row font-semibold">
                      <span>Total price:</span>
                      <span>{{ totalEnterprisePrice }}</span>
                    </div>
                    <div class="summary-row text-sm text-gray-500 mt-1">
                      <span>Valid until:</span>
                      <span>{{ enterpriseExpiryDate }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Top Up Token Section -->
            <div class="top-up-section">
              <div class="top-up-divider">
                <span>Token Top Up</span>
              </div>

              <div
                class="plan-card top-up-card"
                :class="{
                  'selected': selectedPlan?.id === 'topup',
                  'disabled': topUpPlan.disabled
                }"
                @click="handlePlanSelect(topUpPlan)"
              >
                <div class="plan-header">
                  <h4>{{ topUpPlan.name }}</h4>
                  <span class="plan-price">{{ topUpPlan.price }}</span>
                  <span v-if="topUpPlan.disabled" class="disabled-note">
                    (Only available for Enterprise plan)
                  </span>
                </div>
                <ul class="plan-features">
                  <li v-for="feature in topUpPlan.features" :key="feature">
                    <span class="material-icons">check_circle</span>
                    {{ feature }}
                  </li>
                </ul>

                <!-- Top Up Calculator -->
                <div v-if="showTopUpCalculator" class="top-up-calculator">
                  <div class="calculator-header">Calculate your top up</div>
                  <div class="calculator-body">
                    <div class="calculator-row">
                      <label>Quantity:</label>
                      <div class="quantity-control">
                        <button
                          class="quantity-btn"
                          @click.stop="topUpQuantity > 1 ? topUpQuantity-- : null"
                        >-</button>
                        <input
                          type="number"
                          v-model="topUpQuantity"
                          min="1"
                          class="quantity-input"
                          @click.stop
                        />
                        <button
                          class="quantity-btn"
                          @click.stop="topUpQuantity++"
                        >+</button>
                      </div>
                    </div>
                    <div class="calculator-row">
                      <label>Total Price:</label>
                      <div class="total-value">{{ totalTopUpPrice }}</div>
                    </div>
                    <div class="calculator-row">
                      <label>Total Tokens:</label>
                      <div class="total-value">{{ totalTopUpTokens }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button class="secondary-button" @click="closeModal" :disabled="loading">
              Cancel
            </button>
            <button
              class="primary-button"
              @click="handleUpgrade"
              :disabled="!selectedPlan || loading"
            >
              <span v-if="loading">
                <span class="loading-spinner"></span>
                Processing...
              </span>
              <span v-else-if="selectedPlan?.id === 'topup'">Top Up Now</span>
              <span v-else>Upgrade to {{ selectedPlan?.name || 'Plan' }}</span>
            </button>
          </div>
        </div>
      </div>
    </transition>
  </template>

  <script setup lang="ts">
  import { ref, computed, defineProps } from 'vue';
  import axios from 'axios';
  import { useAuthStore } from '../../stores/auth';

  const authStore = useAuthStore();
  const loading = ref(false); // Add loading ref

  declare global {
    interface Window {
      snap: {
        pay: (token: string, options: {
          onSuccess?: (result: any) => void;
          onPending?: (result: any) => void;
          onError?: (result: any) => void;
          onClose?: () => void;
        }) => void;
      };
    }
  }

  const props = defineProps({
    currentSubscription: {
      type: Object,
      default: () => ({
        name: 'Free',
        tokenLimit: 100000,
        tokensUsed: 0,
        remainingTokens: 100000,
        endDate: null
      })
    }
  });

  const emit = defineEmits(['close', 'upgrade']);

  interface Plan {
    id: string;
    name: string;
    price: string;
    features: string[];
    disabled?: boolean;
  }

  const plans = computed(() => {
    return [
      {
        id: 'free',
        name: 'Free',
        price: 'No cost',
        features: [
          'No expiration date',
          'On-premise only',
          'Free 100.000 token per month'
        ],
        // Disable Free plan if user is already on Enterprise
        disabled: props.currentSubscription.name.toLowerCase() === 'enterprise'
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        price: 'Rp 3.500.000/month',
        features: [
          'Contract-based expiration (reverts to the free plan upon expiration if not renewed)',
          'On-premise and cloud base (up to 20 users)',
          'Free 1.000.000 tokens per month',
          'User management features'
        ],
        // Don't disable Enterprise plan even if user is already on it
        // This allows them to extend their subscription
        disabled: false
      }
    ];
  });

  const topUpPlan = ref({
    id: 'topup',
    name: 'Top Up Token',
    price: 'Rp 1.000.000',
    features: [
      'For Enterprise package only',
      'Rp 1.000.000 per 1.000.000 tokens',
      'No expiration date'
    ],
    disabled: props.currentSubscription.name !== 'Enterprise'
  });

  const selectedPlan = ref<Plan | null>(null);
  const topUpQuantity = ref(1);
  const showTopUpCalculator = ref(false);
  const paymentMethod = ref('immediate');
  const subscriptionDuration = ref(1); // Default to 1 month

  const totalTopUpPrice = computed(() => {
    return `Rp ${(topUpQuantity.value * 1000000).toLocaleString()}`;
  });

  const totalTopUpTokens = computed(() => {
    return (topUpQuantity.value * 1000000).toLocaleString();
  });

  const totalEnterprisePrice = computed(() => {
    return `Rp ${(subscriptionDuration.value * 3500000).toLocaleString()}`; //coba
  });

  // Calculate the enterprise expiry date based on the current subscription end date
  const enterpriseExpiryDate = computed(() => {
    let date;
    
    // If user has an existing enterprise subscription with a future end date,
    // use that as the base for extension
    if (props.currentSubscription.name === 'Enterprise' && 
        props.currentSubscription.endDate) {
      const currentEndDate = new Date(props.currentSubscription.endDate);
      
      // Only use current end date if it's in the future
      if (currentEndDate > new Date()) {
        date = new Date(currentEndDate);
        date.setMonth(date.getMonth() + subscriptionDuration.value);
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      }
    }
    
    // Otherwise calculate from today
    date = new Date();
    date.setMonth(date.getMonth() + subscriptionDuration.value);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  });

  const closeModal = () => {
    emit('close');
  };

  const handlePlanSelect = (plan: Plan) => {
    // Don't allow selecting disabled plans
    if (plan.disabled) return;
    
    // Don't allow downgrading from Enterprise to Free
    if (props.currentSubscription.name.toLowerCase() === 'enterprise' && plan.id === 'free') {
      return;
    }

    selectedPlan.value = plan;

    if (plan.id === 'topup') {
      showTopUpCalculator.value = true;
    } else {
      showTopUpCalculator.value = false;
    }
  };

  const handleUpgrade = async () => {
    if (!selectedPlan.value) return;
    
    try {
      loading.value = true;
      
      let payload = {};
      
      if (selectedPlan.value.id === 'enterprise') {
        payload = {
          planId: 'enterprise',
          duration: subscriptionDuration.value,
          paymentMethod: paymentMethod.value
        };
      } else if (selectedPlan.value.id === 'topup') {
        payload = {
          planId: 'topup',
          quantity: topUpQuantity.value,
          paymentMethod: paymentMethod.value
        };
      }
      
      console.log('Upgrade payload:', payload);
      
      if (paymentMethod.value === 'invoice') {
        // Create invoice
        const response = await axios.post(
          `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/create-invoice`, 
          payload,
          {
            headers: {
              'Authorization': authStore.getAuthHeader,
              'Content-Type': 'application/json'
            }
          }
        );
        
        console.log('Invoice created:', response.data);
        loading.value = false; // Set loading to false
        
        // Redirect to invoice page or show success message
        window.location.href = response.data.invoiceUrl;
      } else {
        // Create immediate transaction (existing flow)
        const response = await axios.post(
          `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/create-transaction`, 
          payload,
          {
            headers: {
              'Authorization': authStore.getAuthHeader,
              'Content-Type': 'application/json'
            }
          }
        );
        
        console.log('Transaction created:', response.data);
        
        // Open Midtrans Snap popup (existing flow)
        window.snap.pay(response.data.token, {
          onSuccess: async function(result) {
            console.log('Payment success:', result);
            // Store the order ID for reference
            localStorage.setItem('lastOrderId', response.data.orderId);
            // Redirect to payment finish page
            window.location.href = `${window.location.origin}/payment/finish?order_id=${response.data.orderId}&transaction_status=settlement`;
          },
          onPending: function(result) {
            // Payment pending
            console.log('Payment pending:', result);
            localStorage.setItem('lastOrderId', response.data.orderId);
            window.location.href = `${window.location.origin}/payment/finish?order_id=${response.data.orderId}&transaction_status=pending`;
          },
          onError: function(result) {
            console.error('Payment error:', result);
            localStorage.setItem('lastOrderId', response.data.orderId);
            window.location.href = `${window.location.origin}/payment/finish?order_id=${response.data.orderId}&transaction_status=error`;
          },
          onClose: function() {
            // Customer closed the popup without finishing the payment
            console.log('Payment window closed');
            loading.value = false; // Set loading to false when popup is closed
          }
        });
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      loading.value = false; // Set loading to false on error
    }
  };
  </script>

  <style scoped>
  .modal-mask {
    position: fixed;
    z-index: 9998;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: opacity 0.3s ease;
  }

  .modal-container {
    width: 95%;
    max-width: 900px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    max-height: 95vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .modal-header {
    padding: 24px 30px;
    border-bottom: 1px solid #eaedf3;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8fafc;
  }

  .modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
  }

  .modal-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #64748b;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .modal-close:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #334155;
  }

  .modal-body {
    padding: 30px;
    overflow-y: auto;
    flex-grow: 1;
    background-color: #ffffff;
  }

  .plan-options {
    display: grid;
    gap: 24px;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .plan-card {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 28px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
  }

  .plan-card:hover {
    border-color: #E94560;
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.07);
  }

  .plan-card.selected {
    border-color: #E94560;
    background-color: #fef2f2;
    box-shadow: 0 8px 24px rgba(233, 69, 96, 0.1);
  }

  .plan-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 40px 40px 0;
    border-color: transparent #E94560 transparent transparent;
  }

  .plan-card.disabled {
    opacity: 0.7;
    cursor: not-allowed;
    border-color: #cbd5e1;
    background-color: #f8fafc;
  }

  .plan-card.disabled:hover {
    transform: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
    border-color: #cbd5e1;
  }

  .current-plan-badge {
    display: inline-block;
    background-color: #0ea5e9;
    color: white;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
    margin-top: 8px;
    font-weight: 600;
  }

  .disabled-note {
    display: block;
    color: #64748b;
    font-size: 0.85rem;
    margin-top: 8px;
    font-style: italic;
  }

  .plan-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px dashed #e2e8f0;
  }

  .plan-header h4 {
    margin: 0 0 8px 0;
    font-size: 1.3rem;
    font-weight: 700;
    color: #1e293b;
  }

  .plan-price {
    color: #E94560;
    font-weight: 700;
    font-size: 1.2rem;
    display: block;
    margin-top: 6px;
  }

  .free-plan {
    pointer-events: none;
  }

  .free-plan:hover {
    background-color: inherit;
    cursor: inherit;
  }

  .plan-features {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .plan-features li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 14px;
    font-size: 1rem;
    color: #475569;
    line-height: 1.4;
  }

  .plan-features .material-icons {
    color: #10b981;
    margin-right: 10px;
    font-size: 1.1rem;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .modal-footer {
    padding: 24px 30px;
    border-top: 1px solid #eaedf3;
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    background-color: #f8fafc;
  }

  .secondary-button, .primary-button {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
  }

  .secondary-button {
    background-color: #f1f5f9;
    color: #475569;
    border: 1px solid #cbd5e1;
  }

  .secondary-button:hover {
    background-color: #e2e8f0;
  }

  .primary-button {
    background-color: #E94560;
    color: white;
  }

  .primary-button:hover {
    background-color: #d63553;
    box-shadow: 0 4px 12px rgba(233, 69, 96, 0.2);
  }

  .primary-button:disabled {
    background-color: #fda4b4;
    cursor: not-allowed;
    box-shadow: none;
  }

  /* Top Up Section Styles */
  .top-up-section {
    margin-top: 40px;
  }

  .top-up-divider {
    position: relative;
    text-align: center;
    margin-bottom: 30px;
  }

  .top-up-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: #e2e8f0;
    z-index: 1;
  }

  .top-up-divider span {
    position: relative;
    display: inline-block;
    padding: 0 20px;
    background-color: #fff;
    color: #64748b;
    font-weight: 600;
    font-size: 1.1rem;
    z-index: 2;
  }

  .top-up-card {
    /* background-color: #f8fafc; */
    border: 1px solid #e2e8f0;
  }

  /* Calculator Styles */
  .top-up-calculator {
    margin-top: 24px;
    border-top: 1px dashed #e2e8f0;
    padding-top: 20px;
  }

  .calculator-header {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
    font-size: 1.05rem;
  }

  .calculator-body {
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e2e8f0;
  }

  .calculator-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f1f5f9;
  }

  .calculator-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .calculator-row label {
    font-weight: 500;
    color: #475569;
  }

  .quantity-control {
    display: flex;
    align-items: center;
  }

  .quantity-btn {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    background-color: #f1f5f9;
    border: 1px solid #cbd5e1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    user-select: none;
  }

  .quantity-input {
    width: 60px;
    height: 32px;
    text-align: center;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    margin: 0 8px;
    font-size: 1rem;
  }

  .total-value {
    font-weight: 600;
    color: #E94560;
  }

  /* Animation */
  .modal-enter-active,
  .modal-leave-active {
    transition: opacity 0.4s ease;
  }

  .modal-enter-from,
  .modal-leave-to {
    opacity: 0;
  }

  .modal-enter-active .modal-container {
    animation: modal-in 0.4s ease forwards;
  }

  .modal-leave-active .modal-container {
    animation: modal-out 0.3s ease forwards;
  }

  @keyframes modal-in {
    0% {
      opacity: 0;
      transform: scale(0.95) translateY(20px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes modal-out {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(0.95) translateY(20px);
    }
  }

  .payment-option {
    display: flex;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
  }

  .payment-option.selected {
    border-color: #4f46e5;
    background-color: #eef2ff;
  }

  .payment-option .icon {
    font-size: 1.5rem;
    margin-right: 1rem;
  }

  .payment-option h4 {
    font-weight: 500;
    margin-bottom: 0.25rem;
  }

  .payment-option p {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .subscription-duration {
    margin-top: 20px;
  }

  .duration-header {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.95rem;
  }

  .duration-options {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-top: 10px;
  }

  .duration-option {
    padding: 8px 4px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s;
  }

  .duration-option:hover {
    border-color: #cbd5e1;
    background-color: #f8fafc;
  }

  .duration-option.selected {
    border-color: #E94560;
    background-color: #fef2f2;
    color: #E94560;
    font-weight: 500;
  }

  .duration-summary {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 12px;
    margin-top: 16px;
  }

  .summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
  }

  .summary-row:last-child {
    margin-bottom: 0;
  }

  .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
    vertical-align: middle;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  </style>
