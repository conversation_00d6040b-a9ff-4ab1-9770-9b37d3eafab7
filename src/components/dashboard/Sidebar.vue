<script setup lang="ts">

const props = defineProps<{
  sidebarCollapsed: boolean;
  activeSetting: 'general' | 'api' | 'integrations' | null;
}>();

const emit = defineEmits<{
  'update:sidebarCollapsed': [value: boolean];
  'update:activeSetting': [value: 'general' | 'api' | 'integrations' | null];
  'logout': [];
}>();

const toggleSidebar = () => {
  emit('update:sidebarCollapsed', !props.sidebarCollapsed);
};

const setActiveSetting = (setting: 'general' | 'api' | 'integrations' | null) => {
  emit('update:activeSetting', setting);
};

const logout = () => {
  emit('logout');
};
</script>

<template>
  <aside class="sidebar" :class="{ 'collapsed': sidebarCollapsed }">
    <div class="sidebar-header">
      <h1 class="logo" v-if="!sidebarCollapsed">AgentQ</h1>
      <svg v-else class="magnifying-glass" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
        <path d="M10 18a7.952 7.952 0 004.897-1.688l4.396 4.396 1.414-1.414-4.396-4.396A7.952 7.952 0 0018 10c0-4.411-3.589-8-8-8s-8 3.589-8 8 3.589 8 8 8zm0-14c3.309 0 6 2.691 6 6s-2.691 6-6 6-6-2.691-6-6 2.691-6 6-6z"></path>
      </svg>
      <button class="toggle-button" @click="toggleSidebar">
        <span v-if="sidebarCollapsed">›</span>
        <span v-else>‹</span>
      </button>
    </div>
    
    <div class="sidebar-content">
      <!-- Main menu items -->
      <nav class="sidebar-menu">
        <a href="#" class="menu-item" :class="{ active: !activeSetting }" @click.prevent="setActiveSetting(null)">
          <span class="menu-icon">📋</span>
          <span class="menu-text">AI Generate Test Cases</span>
        </a>
      </nav>
      
      <!-- Bottom menu items -->
      <div class="sidebar-footer">
        <a href="#" class="menu-item" :class="{ active: activeSetting === 'general' }" @click.prevent="setActiveSetting(activeSetting === 'general' ? null : 'general')">
          <span class="menu-icon">⚙️</span>
          <span class="menu-text">Settings</span>
        </a>
        <a v-if="activeSetting !== null" href="#" class="menu-item" :class="{ active: activeSetting === 'api' }" @click.prevent="setActiveSetting('api')">
          <span class="menu-icon">🔑</span>
          <span class="menu-text">API Key</span>
        </a>
        <a v-if="activeSetting !== null" href="#" class="menu-item" :class="{ active: activeSetting === 'integrations' }" @click.prevent="setActiveSetting('integrations')">
          <span class="menu-icon">🔗</span>
          <span class="menu-text">3rd Party Integration</span>
        </a>
        <a href="#" class="menu-item" @click.prevent="logout">
          <span class="menu-icon">🚪</span>
          <span class="menu-text">Sign Out</span>
        </a>
      </div>
    </div>
  </aside>
</template>

<style lang="scss" scoped>
.sidebar {
  width: 260px;
  background-color: white;
  color: black;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  
  &.collapsed {
    width: 70px;
    
    .menu-text {
      display: none;
    }
    
    .logo {
      font-size: 18px;
    }
    
    .sidebar-header {
      padding: 16px 10px;
    }
  }
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.logo {
  font-size: 22px;
  font-weight: bold;
  color: #e94560;
  margin: 0;
}

.magnifying-glass {
  width: 32px;
  height: 32px;
  fill: #e94560;
}

.toggle-button {
  background: none;
  border: none;
  color: black;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 4px;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  overflow-y: auto;
}

.sidebar-menu {
  padding: 20px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #374151;
  text-decoration: none;
  transition: all 0.2s;
  
  &:hover, &.active {
    background-color: #f9fafb;
    color: black;
  }
  
  &.active {
    border-left: 3px solid #e94560;
    background-color: #f9fafb;
    font-weight: 500;
  }
}

.menu-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.sidebar-menu .menu-item .menu-icon {
  margin-right: 10px;
}

.sidebar-footer {
  margin-top: auto;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 10px;
}

// Responsive styles
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    position: fixed;
    z-index: 1000;
    
    &.collapsed {
      transform: translateX(-100%);
    }
  }
  
  .sidebar.collapsed.mobile-open {
    transform: translateX(0);
    width: 260px;
    
    .menu-text {
      display: inline;
    }
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
}
</style>