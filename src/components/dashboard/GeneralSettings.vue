<script setup lang="ts">
const emit = defineEmits<{
  'update:activeSetting': [value: 'api' | 'integrations'];
}>();

const navigateTo = (setting: 'api' | 'integrations') => {
  emit('update:activeSetting', setting);
};
</script>

<template>
  <div class="settings-page">
    <h2>General Settings</h2>
    <p class="settings-description">Configure your account and application settings.</p>
    
    <div class="settings-card">
      <h3>Account Settings</h3>
      <p>Manage your account preferences and personal information.</p>
      <ul class="settings-list">
        <li><a href="#" @click.prevent="navigateTo('api')">API Keys</a></li>
        <li><a href="#" @click.prevent="navigateTo('integrations')">3rd Party Integrations</a></li>
      </ul>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.settings-page {
  h2 {
    margin-bottom: 8px;
    font-size: 24px;
  }
  
  .settings-description {
    color: #6b7280;
    margin-bottom: 24px;
  }
}

.settings-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin-bottom: 12px;
    font-size: 18px;
    color: #374151;
  }
  
  p {
    margin-bottom: 8px;
  }
}

.settings-list {
  list-style: none;
  padding: 0;
  
  li {
    padding: 10px 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    a {
      color: #e94560;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>