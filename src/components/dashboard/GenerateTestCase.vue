<script setup lang="ts">
import { ref, onMounted } from 'vue';
import axios from 'axios';

// const props = defineProps<{
//   userData: { email: string; name?: string } | null;
// }>();

const selectedOption = ref('upload'); // 'upload', 'public-url', 'private-url'
const file = ref<File | null>(null);
const publicUrl = ref('');
const privateUrl = ref('');
const selectedIntegration = ref('');
const loading = ref(false);
const error = ref('');
const success = ref('');
const integrations = ref<Array<{ id: string; type: string; email: string }>>([]);

// Allowed file types
const allowedFileTypes = [
  'application/msword', // .doc
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'text/markdown', // .md
  'text/plain', // .txt
  'application/pdf', // .pdf
  'text/html', // .html
  'application/vnd.ms-excel', // .xls
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'text/csv' // .csv
];

const allowedExtensions = ['.doc', '.docx', '.md', '.txt', '.pdf', '.html', '.xls', '.xlsx', '.csv'];

const fetchIntegrations = async () => {
  try {
    loading.value = true;
    error.value = '';
    const response = await axios.get(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/integrations`);
    integrations.value = response.data;
  } catch (err: any) {
    error.value = 'Failed to load integrations. Please try again.';
    console.error('Error fetching integrations:', err);
  } finally {
    loading.value = false;
  }
};

const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files?.length) return;
  
  const selectedFile = input.files[0];
  
  if (!allowedFileTypes.includes(selectedFile.type) && 
      !allowedExtensions.some(ext => selectedFile.name.toLowerCase().endsWith(ext))) {
    error.value = 'Invalid file type. Please upload a supported document format.';
    file.value = null;
    return;
  }
  
  file.value = selectedFile;
  error.value = '';
};

const generateTestCases = async () => {
  try {
    loading.value = true;
    error.value = '';
    success.value = '';

    if (selectedOption.value === 'upload' && file.value) {
      const formData = new FormData();
      formData.append('file', file.value);
      await axios.post(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/test-cases/upload`, formData);
    } else if (selectedOption.value === 'public-url' && publicUrl.value) {
      await axios.post(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/test-cases/public-url`, { url: publicUrl.value });
    } else if (selectedOption.value === 'private-url' && privateUrl.value && selectedIntegration.value) {
      await axios.post(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/test-cases/private-url`, {
        url: privateUrl.value,
        integrationId: selectedIntegration.value
      });
    } else {
      throw new Error('Please provide all required information');
    }

    success.value = 'Test cases generation started successfully!';
    // Reset form
    file.value = null;
    publicUrl.value = '';
    privateUrl.value = '';
    selectedIntegration.value = '';
  } catch (err: any) {
    error.value = err.response?.data?.message || err.message || 'Failed to generate test cases';
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchIntegrations();
});
</script>

<template>
  <div class="generate-test-case">
    <h2>Generate Test Cases</h2>
    <p class="description">Select your preferred method to generate test cases using AI.</p>

    <!-- Selection Options -->
    <div class="options-container">
      <div class="option-buttons">
        <button 
          :class="['option-button', { active: selectedOption === 'upload' }]"
          @click="selectedOption = 'upload'"
        >
          Upload File
        </button>
        <button 
          :class="['option-button', { active: selectedOption === 'public-url' }]"
          @click="selectedOption = 'public-url'"
        >
          Public URL
        </button>
        <button 
          :class="['option-button', { active: selectedOption === 'private-url' }]"
          @click="selectedOption = 'private-url'"
        >
          Private URL
        </button>
      </div>

      <!-- Upload File Option -->
      <div v-if="selectedOption === 'upload'" class="option-content">
        <h3>Upload Document</h3>
        <p class="supported-formats">
          Supported formats: .doc, .docx, .md, .txt, .pdf, .html, .xls, .xlsx, .csv
        </p>
        <div class="file-upload">
          <input 
            type="file" 
            id="file-input" 
            @change="handleFileChange"
            accept=".doc,.docx,.md,.txt,.pdf,.html,.xls,.xlsx,.csv"
          />
          <label for="file-input" class="file-label">
            <span v-if="!file">Choose a file</span>
            <span v-else>{{ file.name }}</span>
          </label>
        </div>
      </div>

      <!-- Public URL Option -->
      <div v-if="selectedOption === 'public-url'" class="option-content">
        <h3>Enter Public URL</h3>
        <p class="url-description">Enter the URL of a publicly accessible webpage</p>
        <input 
          type="url" 
          v-model="publicUrl" 
          placeholder="https://example.com/documentation"
          class="url-input"
        />
      </div>

      <!-- Private URL Option -->
      <div v-if="selectedOption === 'private-url'" class="option-content">
        <h3>Private URL with Integration</h3>
        <p class="integration-description">Select an integration and enter the private URL</p>
        <div class="integration-container">
          <div v-if="integrations.length === 0" class="no-integrations">
            <p>No integrations found. Please set up integrations in the settings first.</p>
          </div>
          <template v-else>
            <select 
              v-model="selectedIntegration"
              class="integration-select"
            >
              <option value="">Select Integration</option>
              <option 
                v-for="integration in integrations" 
                :key="integration.id" 
                :value="integration.id"
              >
                {{ integration.type }} ({{ integration.email }})
              </option>
            </select>
            <input 
              type="url" 
              v-model="privateUrl" 
              placeholder="Enter private URL"
              class="url-input"
              :disabled="!selectedIntegration"
            />
          </template>
        </div>
      </div>

      <!-- Error and Success Messages -->
      <div v-if="error" class="error-message">{{ error }}</div>
      <div v-if="success" class="success-message">{{ success }}</div>

      <!-- Generate Button -->
      <button 
        class="generate-button" 
        @click="generateTestCases"
        :disabled="loading || (
          (selectedOption === 'upload' && !file) ||
          (selectedOption === 'public-url' && !publicUrl) ||
          (selectedOption === 'private-url' && (!privateUrl || !selectedIntegration))
        )"
      >
        {{ loading ? 'Generating...' : 'Generate Test Cases' }}
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.generate-test-case {
  padding: 20px;
  
  h2 {
    margin-bottom: 8px;
    font-size: 24px;
    color: #374151;
  }
  
  .description {
    color: #6b7280;
    margin-bottom: 24px;
  }
}

.options-container {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.option-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  
  .option-button {
    padding: 12px 20px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    background-color: white;
    color: #374151;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      background-color: #f9fafb;
    }
    
    &.active {
      background-color: #e94560;
      color: white;
      border-color: #e94560;
    }
  }
}

.option-content {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 24px;
  
  h3 {
    margin-bottom: 12px;
    font-size: 18px;
    color: #374151;
  }
}

.supported-formats {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 16px;
}

.file-upload {
  input[type="file"] {
    display: none;
  }
  
  .file-label {
    display: block;
    padding: 12px;
    border: 2px dashed #e5e7eb;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s;
    
    &:hover {
      border-color: #e94560;
      color: #e94560;
    }
  }
}

.url-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 16px;
  
  &:focus {
    outline: none;
    border-color: #e94560;
    box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
  }
  
  &:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
  }
}

.integration-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.no-integrations {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px dashed #e5e7eb;
}

.integration-select {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 16px;
  background-color: white;
  
  &:focus {
    outline: none;
    border-color: #e94560;
    box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.success-message {
  background-color: #dcfce7;
  color: #15803d;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.generate-button {
  width: 100%;
  padding: 14px;
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #d63553;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.url-description, .integration-description {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 16px;
}
</style>