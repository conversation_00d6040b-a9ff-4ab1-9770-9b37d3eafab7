<script setup lang="ts">
import { ref, onMounted } from 'vue';
import axios from 'axios';
import ConfirmationModal from '../common/ConfirmationModal.vue';

interface ApiKey {
  id: string;
  provider: string;
  apiKey: string;
}

const API_URL = `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/api-keys`;

const selectedApiProvider = ref('agentq');
const apiKey = ref('');
const savedApiKeys = ref<ApiKey[]>([]);
const editingId = ref<string | null>(null);
const loading = ref(false);
const error = ref('');

// Modal state
const showDeleteModal = ref(false);
const deletingApiKey = ref<ApiKey | null>(null);

const fetchApiKeys = async () => {
  try {
    loading.value = true;
    const response = await axios.get(API_URL);
    savedApiKeys.value = response.data;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch API keys';
  } finally {
    loading.value = false;
  }
};

const saveApiKey = async () => {
  try {
    loading.value = true;
    error.value = '';

    const payload = {
      provider: selectedApiProvider.value,
      apiKey: apiKey.value
    };

    if (editingId.value) {
      await axios.patch(`${API_URL}/${editingId.value}`, payload);
    } else {
      await axios.post(API_URL, payload);
    }

    await fetchApiKeys();
    resetForm();
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to save API key';
  } finally {
    loading.value = false;
  }
};

const editApiKey = (apiKeyData: ApiKey) => {
  editingId.value = apiKeyData.id;
  apiKey.value = apiKeyData.apiKey;
  selectedApiProvider.value = apiKeyData.provider;
};

const confirmDelete = (apiKeyData: ApiKey) => {
  deletingApiKey.value = apiKeyData;
  showDeleteModal.value = true;
};

const handleDeleteConfirm = async () => {
  if (!deletingApiKey.value) return;
  
  try {
    loading.value = true;
    error.value = '';
    
    await axios.delete(`${API_URL}/${deletingApiKey.value.id}`);
    await fetchApiKeys();
    
    showDeleteModal.value = false;
    deletingApiKey.value = null;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to delete API key';
  } finally {
    loading.value = false;
  }
};

const handleDeleteCancel = () => {
  showDeleteModal.value = false;
  deletingApiKey.value = null;
};

const resetForm = () => {
  editingId.value = null;
  apiKey.value = '';
  selectedApiProvider.value = 'agentq';
};

const cancelEdit = () => {
  resetForm();
};

onMounted(() => {
  fetchApiKeys();
});
</script>

<template>
  <div class="settings-page">
    <h2>API Key Settings</h2>
    <p class="settings-description">Configure your API keys for different AI providers.</p>
    
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
    
    <div class="settings-card">
      <div class="provider-selector">
        <label for="api-provider">Select AI Provider:</label>
        <select
          id="api-provider"
          v-model="selectedApiProvider"
          class="provider-select"
        >
          <option value="agentq">AgentQ</option>
          <option value="gemini">Gemini</option>
          <option value="gpt">GPT</option>
          <option value="deepseek">DeepSeek</option>
        </select>
      </div>

      <div class="api-key-form">
        <h3>{{ editingId ? 'Edit' : 'Add' }} API Key</h3>
        <div class="form-group">
          <label for="api-key">API Key</label>
          <input 
            type="password" 
            id="api-key" 
            v-model="apiKey"
            placeholder="Enter your API key"
            class="form-input"
          />
        </div>
        
        <div class="form-actions">
          <button 
            class="save-button" 
            @click="saveApiKey"
            :disabled="loading || !apiKey"
          >
            {{ editingId ? 'Update' : 'Save' }} API Key
          </button>
          <button 
            v-if="editingId" 
            class="cancel-button" 
            @click="cancelEdit"
            :disabled="loading"
          >
            Cancel
          </button>
        </div>
      </div>
      
      <!-- Loading State -->
      <div v-if="loading" class="loading-state">
        Loading...
      </div>
      
      <!-- Saved API Keys Table -->
      <div class="saved-api-keys" v-if="savedApiKeys.length > 0">
        <h3>Saved API Keys</h3>
        <table class="api-keys-table">
          <thead>
            <tr>
              <th>Provider</th>
              <th>API Key</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="key in savedApiKeys" :key="key.id">
              <td>{{ key.provider }}</td>
              <td>••••••••••••</td>
              <td class="actions">
                <button 
                  class="action-button edit" 
                  @click="editApiKey(key)"
                  :disabled="loading"
                >
                  ✏️ Edit
                </button>
                <button 
                  class="action-button delete" 
                  @click="confirmDelete(key)"
                  :disabled="loading"
                >
                  🗑️ Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- Confirmation Modal -->
    <ConfirmationModal
      :is-open="showDeleteModal"
      title="Delete API Key"
      :message="`Are you sure you want to delete the API key for ${deletingApiKey?.provider}? This action cannot be undone.`"
      @confirm="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
    />
  </div>
</template>

<style lang="scss" scoped>
.settings-page {
  h2 {
    margin-bottom: 8px;
    font-size: 24px;
  }
  
  .settings-description {
    color: #6b7280;
    margin-bottom: 24px;
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.loading-state {
  text-align: center;
  padding: 20px;
  color: #6b7280;
}

.settings-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin-bottom: 12px;
    font-size: 18px;
    color: #374151;
  }
}

.provider-selector {
  margin-bottom: 24px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .provider-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
    background-color: white;
    
    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }
}

.form-group {
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .form-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  
  button {
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .save-button {
    background-color: #e94560;
    color: white;
    border: none;
    
    &:hover:not(:disabled) {
      background-color: #d63553;
    }
  }
  
  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    
    &:hover:not(:disabled) {
      background-color: #e5e7eb;
    }
  }
}

.saved-api-keys {
  margin-top: 32px;
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
  
  h3 {
    margin-bottom: 16px;
  }
}

.api-keys-table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }
  
  th {
    font-weight: 500;
    color: #374151;
    background-color: #f9fafb;
  }
  
  .actions {
    display: flex;
    gap: 8px;
  }
  
  .action-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid #e5e7eb;
    background-color: white;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &.edit {
      color: #059669;
      
      &:hover:not(:disabled) {
        background-color: #f0fdf4;
      }
    }
    
    &.delete {
      color: #dc2626;
      
      &:hover:not(:disabled) {
        background-color: #fef2f2;
      }
    }
  }
}
</style>