<script setup lang="ts">
import { ref, onMounted } from 'vue';
import axios from 'axios';
import ConfirmationModal from '../common/ConfirmationModal.vue';

interface Integration {
  id: string;
  type: string;
  email: string;
  apiToken: string;
}

const API_URL = `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/integrations`;

const selectedIntegration = ref('confluence');
const email = ref('');
const apiToken = ref('');
const savedCredentials = ref<Integration[]>([]);
const editingId = ref<string | null>(null);
const loading = ref(false);
const error = ref('');

// Modal state
const showDeleteModal = ref(false);
const deletingIntegration = ref<Integration | null>(null);

const fetchIntegrations = async () => {
  try {
    loading.value = true;
    const response = await axios.get(API_URL);
    savedCredentials.value = response.data;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch integrations';
  } finally {
    loading.value = false;
  }
};

const saveCredential = async () => {
  try {
    loading.value = true;
    error.value = '';

    const payload = {
      type: selectedIntegration.value,
      email: email.value,
      apiToken: apiToken.value
    };

    if (editingId.value) {
      await axios.patch(`${API_URL}/${editingId.value}`, payload);
    } else {
      await axios.post(API_URL, payload);
    }

    await fetchIntegrations();
    resetForm();
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to save integration';
  } finally {
    loading.value = false;
  }
};

const editCredential = (credential: Integration) => {
  editingId.value = credential.id;
  email.value = credential.email;
  apiToken.value = credential.apiToken;
  selectedIntegration.value = credential.type;
};

const confirmDelete = (integration: Integration) => {
  deletingIntegration.value = integration;
  showDeleteModal.value = true;
};

const handleDeleteConfirm = async () => {
  if (!deletingIntegration.value) return;
  
  try {
    loading.value = true;
    error.value = '';
    
    await axios.delete(`${API_URL}/${deletingIntegration.value.id}`);
    await fetchIntegrations();
    
    showDeleteModal.value = false;
    deletingIntegration.value = null;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to delete integration';
  } finally {
    loading.value = false;
  }
};

const handleDeleteCancel = () => {
  showDeleteModal.value = false;
  deletingIntegration.value = null;
};

const resetForm = () => {
  editingId.value = null;
  email.value = '';
  apiToken.value = '';
  selectedIntegration.value = 'confluence';
};

const cancelEdit = () => {
  resetForm();
};

onMounted(() => {
  fetchIntegrations();
});
</script>

<template>
  <div class="settings-page">
    <h2>3rd Party Integrations</h2>
    <p class="settings-description">Connect your account with third-party services.</p>
    
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
    
    <div class="settings-card">
      <div class="integration-selector">
        <label for="integration-provider">Select Integration:</label>
        <select 
          id="integration-provider" 
          v-model="selectedIntegration" 
          class="integration-select"
        >
          <option value="confluence">Confluence</option>
        </select>
      </div>
      
      <div class="integration-form" v-if="selectedIntegration === 'confluence'">
        <h3>{{ editingId ? 'Edit' : 'Add' }} Confluence Integration</h3>
        <div class="form-group">
          <label for="confluence-email">Email</label>
          <input 
            type="email" 
            id="confluence-email" 
            v-model="email"
            placeholder="Enter your Confluence email"
            class="form-input"
          />
        </div>
        
        <div class="form-group">
          <label for="confluence-token">API Token</label>
          <input 
            type="password" 
            id="confluence-token" 
            v-model="apiToken"
            placeholder="Enter your Confluence API token"
            class="form-input"
          />
        </div>
        
        <div class="form-actions">
          <button 
            class="save-button" 
            @click="saveCredential"
            :disabled="loading || !email || !apiToken"
          >
            {{ editingId ? 'Update' : 'Save' }} Credentials
          </button>
          <button 
            v-if="editingId" 
            class="cancel-button" 
            @click="cancelEdit"
            :disabled="loading"
          >
            Cancel
          </button>
        </div>
      </div>
      
      <!-- Loading State -->
      <div v-if="loading" class="loading-state">
        Loading...
      </div>
      
      <!-- Saved Credentials Table -->
      <div class="saved-credentials" v-if="savedCredentials.length > 0">
        <h3>Saved Credentials</h3>
        <table class="credentials-table">
          <thead>
            <tr>
              <th>Type</th>
              <th>Email</th>
              <th>API Token</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="credential in savedCredentials" :key="credential.id">
              <td>{{ credential.type }}</td>
              <td>{{ credential.email }}</td>
              <td>••••••••••••</td>
              <td class="actions">
                <button 
                  class="action-button edit" 
                  @click="editCredential(credential)"
                  :disabled="loading"
                >
                  ✏️ Edit
                </button>
                <button 
                  class="action-button delete" 
                  @click="confirmDelete(credential)"
                  :disabled="loading"
                >
                  🗑️ Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- Confirmation Modal -->
    <ConfirmationModal
      :is-open="showDeleteModal"
      title="Delete Integration"
      :message="`Are you sure you want to delete the ${deletingIntegration?.type} integration for ${deletingIntegration?.email}? This action cannot be undone.`"
      @confirm="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
    />
  </div>
</template>

<style lang="scss" scoped>
.settings-page {
  h2 {
    margin-bottom: 8px;
    font-size: 24px;
  }
  
  .settings-description {
    color: #6b7280;
    margin-bottom: 24px;
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.loading-state {
  text-align: center;
  padding: 20px;
  color: #6b7280;
}

.settings-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin-bottom: 12px;
    font-size: 18px;
    color: #374151;
  }
}

.integration-selector {
  margin-bottom: 24px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .integration-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
    background-color: white;
    
    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }
}

.form-group {
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .form-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  
  button {
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .save-button {
    background-color: #e94560;
    color: white;
    border: none;
    
    &:hover:not(:disabled) {
      background-color: #d63553;
    }
  }
  
  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    
    &:hover:not(:disabled) {
      background-color: #e5e7eb;
    }
  }
}

.saved-credentials {
  margin-top: 32px;
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
  
  h3 {
    margin-bottom: 16px;
  }
}

.credentials-table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }
  
  th {
    font-weight: 500;
    color: #374151;
    background-color: #f9fafb;
  }
  
  .actions {
    display: flex;
    gap: 8px;
  }
  
  .action-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid #e5e7eb;
    background-color: white;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &.edit {
      color: #059669;
      
      &:hover:not(:disabled) {
        background-color: #f0fdf4;
      }
    }
    
    &.delete {
      color: #dc2626;
      
      &:hover:not(:disabled) {
        background-color: #fef2f2;
      }
    }
  }
}
</style>