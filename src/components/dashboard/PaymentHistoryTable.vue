<template>
  <div class="payment-history">
    <h2 class="text-xl font-semibold text-gray-900 mb-4">Payments</h2>
    <p class="text-sm text-gray-500 mb-6">Review your payment history</p>
    
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valid Until</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-if="loading">
            <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">Loading payment history...</td>
          </tr>
          <tr v-else-if="payments.length === 0">
            <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">No payment history found</td>
          </tr>
          <tr v-for="payment in payments" :key="payment.id" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDate(payment.created_at) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatPlanName(payment.plan_id) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Rp {{ formatAmount(payment.amount) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDuration(payment) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ payment.valid_until ? formatDate(payment.valid_until) : '-' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="getStatusClass(payment.status)">
                {{ payment.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <a v-if="payment.invoice_url" :href="payment.invoice_url" target="_blank" class="text-green-600 hover:text-green-900">
                View
              </a>
              <span v-else>-</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <button 
                v-if="payment.status === 'pending'" 
                @click="checkPaymentStatus(payment.order_id)"
                class="text-indigo-600 hover:text-indigo-900 mr-3"
              >
                Check Status
              </button>
              <button 
                v-if="payment.status === 'pending'" 
                @click="openPaymentPage(payment.order_id)"
                class="text-blue-600 hover:text-blue-900"
              >
                Pay Now
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <div class="flex justify-between items-center mt-4">
      <div class="text-sm text-gray-500">
        Page {{ currentPage }} of {{ totalPages }}
      </div>
      <div>
        <button 
          @click="loadPage(currentPage - 1)" 
          :disabled="currentPage === 1"
          class="px-3 py-1 border rounded mr-2"
          :class="currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''"
        >
          Previous
        </button>
        <button 
          @click="loadPage(currentPage + 1)" 
          :disabled="currentPage === totalPages"
          class="px-3 py-1 border rounded"
          :class="currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useAuthStore } from '../../stores/auth';

const authStore = useAuthStore();
const payments = ref<any[]>([]);
const loading = ref(true);
const currentPage = ref(1);
const totalPages = ref(1);
const pageSize = 10; // Set page size to 10

onMounted(() => {
  loadPayments();
  
  // Check for pending payments every 60 seconds
  const intervalId = setInterval(() => {
    checkPendingPayments();
  }, 60000);
  
  // Clear interval on component unmount
  return () => clearInterval(intervalId);
});

const loadPayments = async () => {
  try {
    loading.value = true;
    const response = await axios.get(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/history?page=${currentPage.value}&limit=${pageSize}`,
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    payments.value = response.data.items;
    totalPages.value = response.data.meta.totalPages;
    loading.value = false;
  } catch (error) {
    console.error('Error loading payment history:', error);
    loading.value = false;
  }
};

const loadPage = (page: number) => {
  if (page < 1 || page > totalPages.value) return;
  currentPage.value = page;
  loadPayments();
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

const formatAmount = (amount: number | string): string => {
  // Ensure amount is treated as a number
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  // Format with thousands separator and 2 decimal places
  return numAmount.toLocaleString('id-ID', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  });
};

const getStatusClass = (status: string) => {
  switch (status.toLowerCase()) {
    case 'paid':
    case 'settlement':
    case 'capture':
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800';
    case 'pending':
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800';
    case 'failed':
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800';
    case 'refunded':
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800';
    default:
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800';
  }
};

const checkPaymentStatus = async (orderId: string) => {
  try {
    loading.value = true;
    const response = await axios.get(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/check-status/${orderId}`,
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    if (response.data.status === 'success') {
      // Refresh user profile to get updated subscription info
      await authStore.checkAuth();
      // Reload payments to show updated status
      loadPayments();
    }
    
    loading.value = false;
  } catch (error) {
    console.error('Error checking payment status:', error);
    loading.value = false;
  }
};

const openPaymentPage = (orderId: string) => {
  window.location.href = `${window.location.origin}/payment/resume?order_id=${orderId}`;
};

const checkPendingPayments = async () => {
  try {
    // Find all pending payments
    const pendingPayments = payments.value.filter(p => 
      p.status.toLowerCase() === 'pending'
    );
    
    // Check status for each pending payment
    for (const payment of pendingPayments) {
      await checkPaymentStatus(payment.order_id);
    }
  } catch (error) {
    console.error('Error checking pending payments:', error);
  }
};

const formatPlanName = (planId: string) => {
  if (planId === 'enterprise') {
    return 'Enterprise Plan';
  } else if (planId === 'topup') {
    return 'Token Top-up';
  }
  return planId;
};

const formatDuration = (payment: any) => {
  if (payment.plan_id === 'enterprise') {
    const duration = payment.duration || 1;
    return `${duration} ${duration === 1 ? 'month' : 'months'}`;
  } else if (payment.plan_id === 'topup') {
    const quantity = payment.quantity || 1;
    return `${quantity}M tokens`;
  }
  return '-';
};
</script>
