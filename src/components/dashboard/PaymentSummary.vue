<template>
  <div class="bg-gray-800 rounded-lg shadow-md p-4">
    <h3 class="text-lg font-semibold text-white mb-3">Recent Payments</h3>
    
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-700">
        <thead>
          <tr>
            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-400">Date</th>
            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-400">Plan</th>
            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-400">Amount</th>
            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-400">Duration</th>
            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-400">Status</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-700">
          <tr v-if="loading">
            <td colspan="5" class="px-4 py-3 text-center text-sm text-gray-400">Loading...</td>
          </tr>
          <tr v-else-if="payments.length === 0">
            <td colspan="5" class="px-4 py-3 text-center text-sm text-gray-400">No payment history</td>
          </tr>
          <tr v-for="payment in payments.slice(0, 3)" :key="payment.id" class="hover:bg-gray-800">
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
              {{ formatDate(payment.created_at) }}
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
              {{ formatPlanName(payment.plan_id) }}
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
              Rp {{ formatAmount(payment.amount) }}
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
              {{ formatDuration(payment) }}
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm">
              <span :class="getStatusClass(payment.status)">
                {{ formatStatus(payment.status) }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <div v-if="hasPendingPayments" class="mt-4 p-3 bg-yellow-900 bg-opacity-30 rounded-md text-sm">
      <p class="text-yellow-300 mb-2">
        <span class="material-icons text-sm align-middle mr-1">info</span>
        You have pending payments that need attention
      </p>
      <router-link to="/dashboard" class="text-white bg-yellow-600 hover:bg-yellow-500 px-3 py-1 rounded-md inline-block">
        View Pending Payments
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';
import { useAuthStore } from '../../stores/auth';

const authStore = useAuthStore();
const payments = ref<any[]>([]);
const loading = ref(true);

onMounted(() => {
  loadRecentPayments();
});

const loadRecentPayments = async () => {
  try {
    loading.value = true;
    const response = await axios.get(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/history?page=1&limit=3`,
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    payments.value = response.data.items;
    loading.value = false;
  } catch (error) {
    console.error('Error loading recent payments:', error);
    loading.value = false;
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  });
};

const formatAmount = (amount: number) => {
  return amount.toLocaleString('id-ID');
};

const formatStatus = (status: string) => {
  switch (status.toLowerCase()) {
    case 'settlement':
    case 'capture':
      return 'Paid';
    case 'refund':
      return 'Refunded';
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'Failed';
    default:
      return status.charAt(0).toUpperCase() + status.slice(1);
  }
};

const getStatusClass = (status: string) => {
  switch (status.toLowerCase()) {
    case 'paid':
    case 'settlement':
    case 'capture':
      return 'text-green-500';
    case 'pending':
      return 'text-yellow-500';
    case 'failed':
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'text-red-500';
    case 'refund':
    case 'refunded':
      return 'text-blue-500';
    default:
      return 'text-gray-400';
  }
};

const formatPlanName = (planId: string) => {
  if (planId === 'enterprise') {
    return 'Enterprise Plan';
  } else if (planId === 'topup') {
    return 'Token Top-up';
  }
  return planId;
};

const formatDuration = (payment: any) => {
  if (payment.plan_id === 'enterprise') {
    const duration = payment.duration || 1;
    return `${duration} ${duration === 1 ? 'month' : 'months'}`;
  } else if (payment.plan_id === 'topup') {
    const quantity = payment.quantity || 1;
    return `${quantity}M tokens`;
  }
  return '-';
};

const hasPendingPayments = computed(() => {
  return payments.value.some(p => p.status.toLowerCase() === 'pending');
});
</script>
