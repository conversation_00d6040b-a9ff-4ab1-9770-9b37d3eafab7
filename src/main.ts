import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Enable CORS for the frontend
  app.enableCors({
    origin: ['https://agentq.id', 'http://localhost:5173', 'http://localhost:5174', 'https://www.agentq.id', 'https://app.agentq.id', 'https://staging.agentq.id', 'https://staging-app.agentq.id'],
    methods: ['GET', 'POST', 'PATCH', 'PUT', 'DELETE'],
    credentials: true,
    allowedHeaders: 'Content-Type,Authorization',
  });

  // Log all registered routes for debugging
  const server = app.getHttpServer();
  const router = server._events.request._router;
  console.log('Registered routes:');
  router.stack.forEach(layer => {
    if (layer.route) {
      const path = layer.route.path;
      const methods = Object.keys(layer.route.methods).map(m => m.toUpperCase());
      console.log(`${methods.join(',')} ${path}`);
    }
  });

  // Swagger setup
  const config = new DocumentBuilder()
    .setTitle('AgentQ API')
    .setDescription('The AgentQ API documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(3000);
}
bootstrap();
