<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Header (reuse from HomeView) -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <a href="https://agentq.id" class="text-2xl font-bold text-e94560">AgentQ</a>
          <div class="flex items-center space-x-4">
            <button @click="goBack" class="header-button">
              <span class="material-icons mr-1">arrow_back</span>
              Back
            </button>
            <button @click="handleLogout" class="header-button">
              <span class="material-icons mr-1">logout</span>
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-e94560"></div>
        </div>
        
        <div v-else-if="error" class="text-center py-12">
          <span class="material-icons text-red-500 text-4xl mb-4">error</span>
          <h2 class="text-xl font-semibold text-gray-900 mb-2">Error Loading Invoice</h2>
          <p class="text-gray-500">{{ error }}</p>
        </div>
        
        <div v-else>
          <!-- Print-only logo -->
          <!-- <div class="print-only">
            <img :src="logoUrl" alt="AgentQ Logo" class="print-logo" />
          </div> -->

          <!-- Invoice Header -->
          <div class="flex justify-between items-start mb-8">
            <div class="flex items-center">
              <img :src="logoUrl" alt="AgentQ Logo" class="h-12 mr-4" />
              <div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Invoice</h1>
                <p class="text-gray-500">{{ invoiceData.order_id }}</p>
              </div>
            </div>
            <div class="text-right">
              <span :class="getStatusClass(invoiceData.status)" class="px-3 py-1 rounded-full text-sm font-medium">
                {{ formatStatus(invoiceData.status) }}
              </span>
              <p class="text-gray-500 mt-2">Date: {{ formatDate(invoiceData.created_at) }}</p>
            </div>
          </div>
          
          <!-- Invoice Details -->
          <div class="border-t border-gray-200 pt-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h2 class="text-sm font-medium text-gray-500 mb-2">Billed To</h2>
                <p class="text-gray-900">{{ profileData?.company?.name || 'Your Company' }}</p>
                <p class="text-gray-500">{{ profileData?.email }}</p>
              </div>
              
              <div class="text-right">
                <h2 class="text-sm font-medium text-gray-500 mb-2">Invoice Details</h2>
                <p class="text-gray-900">Amount: Rp {{ formatAmount(invoiceData.amount) }}</p>
                <p class="text-gray-500">
                  <strong>Payment Due Date:</strong> {{ getPaymentDueDate() }}
                </p>
                <p v-if="invoiceData.plan_id === 'enterprise' && getSubscriptionEndDate()" class="text-gray-500">
                  <strong>Subscription End Date:</strong> {{ getSubscriptionEndDate() }}
                </p>
              </div>
            </div>
          </div>
          
          <!-- Invoice Items -->
          <div class="border-t border-gray-200 pt-6 mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Invoice Items</h2>
            
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ getPlanDescription(invoiceData.plan_id, invoiceData.quantity) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                      Rp {{ formatAmount(invoiceData.amount) }}
                    </td>
                  </tr>
                </tbody>
                <tfoot class="bg-gray-50">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">Total</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                      Rp {{ formatAmount(invoiceData.amount) }}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
          
          <!-- Payment Information -->
          <div class="border-t border-gray-200 pt-6 mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Payment Information</h2>
            
            <div v-if="invoiceData.status === 'pending'" class="bg-yellow-50 p-4 rounded-md mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <span class="material-icons text-yellow-400">info</span>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-yellow-800">Payment Pending</h3>
                  <div class="mt-2 text-sm text-yellow-700">
                    <p>This invoice is waiting for payment. Click the button below to complete your payment.</p>
                    <p class="mt-1">
                      <strong>Payment Due Date:</strong> {{ getPaymentDueDate() }}
                    </p>
                    <p v-if="invoiceData.plan_id === 'enterprise' && getSubscriptionEndDate()" class="mt-1">
                      <strong>Subscription End Date:</strong> {{ getSubscriptionEndDate() }}
                    </p>
                    <p class="mt-1" v-if="isInvoiceExpiringSoon">
                      <strong class="text-red-600">Warning:</strong> This invoice will expire soon!
                    </p>
                  </div>
                  <div class="mt-4">
                    <button 
                      @click="openPaymentPage(invoiceData.order_id)"
                      class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                    >
                      Pay Now
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-else-if="invoiceData.status === 'settlement' || invoiceData.status === 'capture' || invoiceData.status === 'paid'" class="bg-green-50 p-4 rounded-md mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <span class="material-icons text-green-400">check_circle</span>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-green-800">Payment Completed</h3>
                  <div class="mt-2 text-sm text-green-700">
                    <p>Thank you for your payment. This invoice has been paid in full.</p>
                    <p v-if="invoiceData.invoiceDetails?.paid_at" class="mt-1">
                      Payment Date: {{ formatDate(invoiceData.invoiceDetails.paid_at) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-else-if="invoiceData.status === 'expire' || invoiceData.status === 'expired'" class="bg-red-50 p-4 rounded-md mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <span class="material-icons text-red-400">error</span>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Invoice Expired</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <p>This invoice has expired. Please contact support if you still wish to make this payment.</p>
                    <p class="mt-1">
                      <strong>Expired On:</strong> {{ getPaymentDueDate() }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-else class="bg-gray-50 p-4 rounded-md mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <span class="material-icons text-gray-400">info</span>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-gray-800">Invoice Status: {{ formatStatus(invoiceData.status) }}</h3>
                  <div class="mt-2 text-sm text-gray-700">
                    <p>Please contact support if you have any questions about this invoice.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Actions -->
          <div class="flex justify-end space-x-4">
            <button 
              @click="printInvoice" 
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <span class="material-icons mr-2">print</span>
              Print
            </button>
            
            <button 
              v-if="invoiceData.status === 'pending'"
              @click="openPaymentPage(invoiceData.order_id)" 
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-e94560 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <span class="material-icons mr-2">payment</span>
              Pay Now
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import axios from 'axios';

// Use a dynamic import for the image
const logoUrl = new URL('../assets/images/agentq-logo.png', import.meta.url).href;

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const loading = ref(true);
const error = ref('');
const invoiceData = ref<any>(null);
const profileData = ref<any>(null);

onMounted(async () => {
  try {
    const invoiceId = route.params.id as string;
    
    if (!invoiceId) {
      throw new Error('Invoice ID is required');
    }
    
    // Ensure auth token is valid before making the request
    await authStore.checkTokenExpiry();
    
    // Fetch invoice details
    const response = await axios.get(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/invoice/${invoiceId}`,
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    invoiceData.value = response.data;
    
    // Debug date information
    console.log('Invoice data:', invoiceData.value);
    console.log('Created at:', invoiceData.value.created_at);
    console.log('Created at (formatted):', formatDate(invoiceData.value.created_at));
    console.log('Valid until:', invoiceData.value.valid_until);
    console.log('Valid until (formatted):', formatDate(invoiceData.value.valid_until));
    
    if (invoiceData.value.invoiceDetails) {
      console.log('Invoice details expiry date:', invoiceData.value.invoiceDetails.expiry_date);
      console.log('Invoice details expiry date (formatted):', 
        formatDate(invoiceData.value.invoiceDetails.expiry_date));
    }
    
    // Calculate due date manually for debugging
    const creationDate = new Date(invoiceData.value.created_at);
    const manualDueDate = new Date(creationDate);
    manualDueDate.setDate(manualDueDate.getDate() + 1);
    console.log('Manual calculation - Creation date:', creationDate);
    console.log('Manual calculation - Due date:', manualDueDate);
    console.log('Manual calculation - Due date (formatted):', 
      formatDate(manualDueDate.toISOString()));
    
    console.log('Calculated due date from function:', getPaymentDueDate());
    
    // Fetch profile data to get company information
    const profileResponse = await axios.get(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/profile`,
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    profileData.value = profileResponse.data;
    
    loading.value = false;
  } catch (err: any) {
    console.error('Error loading invoice details:', err);
    
    // If error is due to authentication, try to refresh token
    if (err.response?.status === 401) {
      const refreshed = await authStore.refreshAuthToken();
      
      if (refreshed) {
        // Retry loading the page
        window.location.reload();
        return;
      } else {
        // If refresh failed, show login required message
        error.value = 'Your session has expired. Please log in again.';
      }
    } else {
      error.value = err.message || 'Failed to load invoice details';
    }
    
    loading.value = false;
  }
});

const handleLogout = () => {
  authStore.logout();
  router.push('/login');
};

const goBack = () => {
  router.back();
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const formatAmount = (amount: string | number): string => {
  // Ensure amount is treated as a number
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // Format with thousands separator and 2 decimal places
  return numAmount.toLocaleString('id-ID', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
};

const formatStatus = (status: string) => {
  switch (status.toLowerCase()) {
    case 'settlement':
    case 'capture':
      return 'Paid';
    case 'refund':
      return 'Refunded';
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'Failed';
    default:
      return status.charAt(0).toUpperCase() + status.slice(1);
  }
};

const getStatusClass = (status: string) => {
  switch (status.toLowerCase()) {
    case 'paid':
    case 'settlement':
    case 'capture':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'failed':
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'bg-red-100 text-red-800';
    case 'refund':
    case 'refunded':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getPlanDescription = (planId: string, quantity: number) => {
  if (planId === 'enterprise') {
    // For enterprise, use the duration field directly instead of quantity
    const duration = invoiceData.value.duration || quantity || 1;
    return `Enterprise Plan Subscription (${duration} ${duration === 1 ? 'month' : 'months'})`;
  } else if (planId === 'topup') {
    return `Token Top-up (${quantity}M tokens)`;
  }
  return planId;
};

const openPaymentPage = (orderId: string) => {
  router.push(`/payment/resume?order_id=${orderId}`);
};

const printInvoice = () => {
  window.print();
};

const getPaymentDueDate = () => {
  // Use payment_due_date if available
  if (invoiceData.value?.payment_due_date) {
    return formatDate(invoiceData.value.payment_due_date);
  } else if (invoiceData.value?.created_at) {
    // If no payment_due_date is available, show creation date + 24 hours as fallback
    const creationDate = new Date(invoiceData.value.created_at);
    const dueDate = new Date(creationDate);
    dueDate.setDate(dueDate.getDate() + 1); // Add exactly 1 day
    return formatDate(dueDate.toISOString());
  } else {
    return 'N/A';
  }
};

const isInvoiceExpiringSoon = computed(() => {
  if (invoiceData.value?.status !== 'pending') return false;
  
  const dueDate = invoiceData.value?.valid_until 
    ? new Date(invoiceData.value.valid_until) 
    : null;
    
  if (!dueDate) return false;
  
  const now = new Date();
  const hoursRemaining = (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  return hoursRemaining > 0 && hoursRemaining < 2; // Less than 2 hours remaining
});

const getSubscriptionEndDate = () => {
  // Use valid_until for subscription end date
  if (invoiceData.value?.valid_until) {
    return formatDate(invoiceData.value.valid_until);
  }
  return null;
};
</script>

<style scoped>
.header-button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.header-button:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.text-e94560 {
  color: #e94560;
}

.bg-e94560 {
  background-color: #e94560;
}

@media print {
  header, .actions {
    display: none;
  }
  
  body {
    background-color: white;
  }
  
  .min-h-screen {
    min-height: auto;
  }
  
  .bg-gray-100 {
    background-color: white;
  }
  
  .bg-white {
    box-shadow: none;
  }
  
  .max-w-7xl {
    max-width: none;
    padding: 0;
  }
  
  /* Add logo for print */
  .print-logo {
    display: block;
    max-width: 150px;
    margin-bottom: 20px;
  }
}

/* Hide print-only elements when not printing */
.print-only {
  display: none;
}

@media print {
  .print-only {
    display: block;
  }
}
</style>
