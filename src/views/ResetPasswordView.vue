<template>
    <div class="login-container">
      <div class="login-header">
        <h1 class="logo">AgentQ</h1>
      </div>
      
      <div class="login-card">
        <template v-if="!success">
          <h2 class="login-title">Set New Password</h2>
          <p class="login-subtitle">Please choose a strong password for your account</p>
          
          <form @submit.prevent="handleSubmit" class="login-form">
            <div class="form-group">
              <label for="password">New Password</label>
              <input 
                type="password" 
                id="password" 
                v-model="password"
                required
                placeholder="Enter new password"
                @input="validatePassword"
              />
              <div class="password-requirements">
                <div :class="{ 'requirement-met': passwordChecks.length }">
                  <span class="material-icons">{{ passwordChecks.length ? 'check_circle' : 'cancel' }}</span>
                  At least 8 characters
                </div>
                <div :class="{ 'requirement-met': passwordChecks.cases }">
                  <span class="material-icons">{{ passwordChecks.cases ? 'check_circle' : 'cancel' }}</span>
                  Mixture of uppercase and lowercase letters
                </div>
                <div :class="{ 'requirement-met': passwordChecks.number }">
                  <span class="material-icons">{{ passwordChecks.number ? 'check_circle' : 'cancel' }}</span>
                  At least one number
                </div>
                <div :class="{ 'requirement-met': passwordChecks.special }">
                  <span class="material-icons">{{ passwordChecks.special ? 'check_circle' : 'cancel' }}</span>
                  At least one special character (!@#$%^&*)
                </div>
              </div>
            </div>
  
            <div class="form-group">
              <label for="confirmPassword">Confirm Password</label>
              <input 
                type="password" 
                id="confirmPassword" 
                v-model="confirmPassword"
                required
                placeholder="Confirm new password"
                @input="validateConfirmPassword"
              />
              <p v-if="confirmPasswordError" class="error-message mt-2">{{ confirmPasswordError }}</p>
            </div>
  
            <div v-if="error" class="error-message">
              {{ error }}
            </div>
            
            <button 
              type="submit" 
              class="login-button"
              :disabled="isLoading || !isValid"
            >
              <span v-if="isLoading" class="loading-spinner"></span>
              <span v-else>Reset Password</span>
            </button>
          </form>
        </template>
  
        <div v-else class="text-center">
          <span class="material-icons text-green-500 text-5xl mb-4">check_circle</span>
          <h3 class="text-xl font-medium text-gray-900 mb-2">Password Reset Successful!</h3>
          
          <br>
          <p class="text-gray-600 mb-6">Your password has been successfully reset.</p>
          
          <br>
          <router-link
            to="/login"
            class="login-button block text-center"
          >
            Continue to Login
          </router-link>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed } from 'vue'
  import {  useRoute } from 'vue-router'
  import { useAuthStore } from '../stores/auth'
  
  const route = useRoute()
  const authStore = useAuthStore()
  
  const password = ref('')
  const confirmPassword = ref('')
  const confirmPasswordError = ref('')
  const error = ref('')
  const success = ref(false)
  const isLoading = ref(false)
  
  const passwordChecks = computed(() => ({
    length: password.value.length >= 8,
    cases: /(?=.*[a-z])(?=.*[A-Z])/.test(password.value),
    number: /\d/.test(password.value),
    special: /[!@#$%^&*]/.test(password.value)
  }))
  
  const isValid = computed(() => {
    return (
      passwordChecks.value.length &&
      passwordChecks.value.cases &&
      passwordChecks.value.number &&
      passwordChecks.value.special &&
      password.value === confirmPassword.value
    )
  })
  
  const validatePassword = () => {
    if (confirmPassword.value) {
      validateConfirmPassword()
    }
  }
  
  const validateConfirmPassword = () => {
    confirmPasswordError.value = password.value === confirmPassword.value ? '' : 'Passwords do not match'
  }
  
  async function handleSubmit() {
    if (!isValid.value) {
      error.value = 'Please ensure all password requirements are met'
      return
    }
  
    const token = route.query.token as string
    if (!token) {
      error.value = 'Invalid reset token'
      return
    }
  
    error.value = ''
    isLoading.value = true
  
    try {
      await authStore.resetPassword(token, password.value)
      success.value = true
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: #f5f7fa;
    padding: 20px;
  }
  
  .login-header {
    margin-bottom: 30px;
    text-align: center;
  }
  
  .logo {
    font-size: 28px;
    font-weight: bold;
    color: #e94560;
  }
  
  .login-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 40px;
    width: 100%;
    max-width: 450px;
  }
  
  .login-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    text-align: center;
  }
  
  .login-subtitle {
    color: #6b7280;
    text-align: center;
    margin-bottom: 24px;
  }
  
  .login-form {
    display: flex;
    flex-direction: column;
  }
  
  .form-group {
    margin-bottom: 16px;
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #374151;
    }
    
    input {
      width: 100%;
      padding: 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      font-size: 16px;
      
      &:focus {
        outline: none;
        border-color: #e94560;
        box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
      }
    }
  }
  
  .password-requirements {
    margin-top: 8px;
    font-size: 14px;
    color: #6b7280;
  
    div {
      display: flex;
      align-items: center;
      margin: 4px 0;
  
      .material-icons {
        font-size: 16px;
        margin-right: 4px;
        color: #ef4444;
      }
  
      &.requirement-met {
        color: #059669;
        
        .material-icons {
          color: #059669;
        }
      }
    }
  }
  
  .error-message {
    background-color: #fee2e2;
    color: #b91c1c;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
  }
  
  .login-button {
    background-color: #e94560;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    
    &:hover:not(:disabled) {
      background-color: #d63553;
    }
  
    &:disabled {
      background-color: #e5e7eb;
      cursor: not-allowed;
    }
  }
  
  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  </style>