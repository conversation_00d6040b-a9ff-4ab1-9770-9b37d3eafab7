<template>
  <div class="login-container">
    <div class="login-header">
      <h1 class="logo">AgentQ</h1>
    </div>
    
    <div class="login-card">
      <h2 class="login-title">Create your account</h2>
      <p class="login-subtitle">Already have an account? <router-link to="/login" class="create-account-link">Sign in</router-link></p>
      
      <div class="social-login">
        <button class="social-button github" @click="loginWithGitHub">
          <img src="../assets/images/github-logo.svg" alt="GitHub Logo" class="icon" />
          Continue with GitHub
        </button>
        
        <button class="social-button google" @click="loginWithGoogle">
          <img src="../assets/images/google-logo.svg" alt="Google Logo" class="icon" />
          Continue with Google
        </button>
      </div>
      
      <div class="divider">
        <span>Or continue with</span>
      </div>
      
      <form @submit.prevent="handleSignup" class="login-form">
        <div class="form-group">
          <label for="name">Full Name</label>
          <input 
            type="text" 
            id="name" 
            v-model="name" 
            required
            placeholder="Enter your full name"
          />
        </div>

        <div class="form-group">
          <label for="email">Email address</label>
          <input 
            type="email" 
            id="email" 
            v-model="email" 
            required
            placeholder="Enter your email"
            @input="validateEmail"
          />
          <p v-if="emailError" class="error-message">{{ emailError }}</p>
        </div>

        <div class="form-group">
          <label for="company">Company / Organization (optional)</label>
          <input 
            type="text" 
            id="company" 
            v-model="company" 
            placeholder="Enter your company name"
          />
        </div>
        
        <div class="form-group">
          <label for="password">Password</label>
          <input 
            type="password" 
            id="password" 
            v-model="password" 
            required
            placeholder="Enter your password"
            @input="validatePassword"
          />
          <div class="password-requirements">
            <div :class="{ 'requirement-met': passwordChecks.length }">
              <span class="material-icons">{{ passwordChecks.length ? 'check_circle' : 'cancel' }}</span>
              At least 8 characters
            </div>
            <div :class="{ 'requirement-met': passwordChecks.cases }">
              <span class="material-icons">{{ passwordChecks.cases ? 'check_circle' : 'cancel' }}</span>
              Mixture of uppercase and lowercase letters
            </div>
            <div :class="{ 'requirement-met': passwordChecks.number }">
              <span class="material-icons">{{ passwordChecks.number ? 'check_circle' : 'cancel' }}</span>
              At least one number
            </div>
            <div :class="{ 'requirement-met': passwordChecks.special }">
              <span class="material-icons">{{ passwordChecks.special ? 'check_circle' : 'cancel' }}</span>
              At least one special character (!@#$%^&*)
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirm Password</label>
          <input 
            type="password" 
            id="confirmPassword" 
            v-model="confirmPassword" 
            required
            placeholder="Confirm your password"
            @input="validateConfirmPassword"
          />
          <p v-if="confirmPasswordError" class="error-message">{{ confirmPasswordError }}</p>
        </div>
        
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>

        <div v-if="successMessage" class="success-message">
          {{ successMessage }}
        </div>
        
        <button 
          type="submit" 
          class="login-button"
          :disabled="!isFormValid || isLoading"
        >
          <span v-if="isLoading" class="loading-spinner"></span>
          <span v-else>Create Account</span>
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const API_URL = `${(import.meta as any).env.VITE_CORE_SERVICE_URL}`
const router = useRouter()

const name = ref('')
const email = ref('')
const company = ref('')
const password = ref('')
const confirmPassword = ref('')
const emailError = ref('')
const confirmPasswordError = ref('')
const errorMessage = ref('')
const successMessage = ref('')
const isLoading = ref(false)

const passwordChecks = computed(() => ({
  length: password.value.length >= 8,
  cases: /(?=.*[a-z])(?=.*[A-Z])/.test(password.value),
  number: /\d/.test(password.value),
  special: /[!@#$%^&*]/.test(password.value)
}))

const isFormValid = computed(() => {
  return (
    name.value &&
    email.value &&
    !emailError.value &&
    password.value &&
    confirmPassword.value &&
    !confirmPasswordError.value &&
    passwordChecks.value.length &&
    passwordChecks.value.cases &&
    passwordChecks.value.number &&
    passwordChecks.value.special
  )
})

const validateEmail = () => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  emailError.value = emailPattern.test(email.value) ? '' : 'Invalid email format'
}

const validatePassword = () => {
  if (confirmPassword.value) {
    validateConfirmPassword()
  }
}

const validateConfirmPassword = () => {
  confirmPasswordError.value = password.value === confirmPassword.value ? '' : 'Passwords do not match'
}

const handleSignup = async () => {
  try {
    isLoading.value = true
    errorMessage.value = ''
    successMessage.value = ''

    const response = await fetch(`${API_URL}/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: name.value,
        email: email.value,
        company: company.value || undefined,
        password: password.value
      })
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.message || 'Signup failed')
    }

    successMessage.value = data.message || 'Registration successful! Please check your email to confirm your account.'
    
    // Clear form
    name.value = ''
    email.value = ''
    company.value = ''
    password.value = ''
    confirmPassword.value = ''

    // Redirect to login after 3 seconds
    setTimeout(() => {
      router.push('/login')
    }, 3000)
  } catch (error: any) {
    errorMessage.value = error.message || 'Signup failed. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const loginWithGitHub = () => {
  window.location.href = `${API_URL}/auth/github`
}

const loginWithGoogle = () => {
  window.location.href = `${API_URL}/auth/google`
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.login-header {
  margin-bottom: 30px;
  text-align: center;
}

.logo {
  font-size: 28px;
  font-weight: bold;
  color: #e94560;
}

.login-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 40px;
  width: 100%;
  max-width: 450px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  text-align: center;
}

.login-subtitle {
  color: #6b7280;
  text-align: center;
  margin-bottom: 24px;
}

.create-account-link {
  color: #e94560;
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background-color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f9fafb;
  }
  
  &.google {
    border-color: #4285f4;
    color: #4285f4;
    
    &:hover {
      background-color: #4285f4;
      color: white;
    }
  }
  
  .icon {
    margin-right: 12px;
    height: 24px;
    width: 24px;
  }
}

.divider {
  display: flex;
  align-items: center;
  margin: 24px 0;
  
  &::before,
  &::after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #e5e7eb;
  }
  
  span {
    padding: 0 12px;
    color: #6b7280;
    font-size: 14px;
  }
}

.login-form {
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 16px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
  }
  
  input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }
}

.password-requirements {
  margin-top: 8px;
  font-size: 14px;
  color: #6b7280;

  div {
    display: flex;
    align-items: center;
    margin: 4px 0;

    .material-icons {
      font-size: 16px;
      margin-right: 4px;
      color: #ef4444;
    }

    &.requirement-met {
      color: #059669;
      
      .material-icons {
        color: #059669;
      }
    }
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 14px;
}

.success-message {
  background-color: #dcfce7;
  color: #15803d;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 14px;
}

.login-button {
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover:not(:disabled) {
    background-color: #d63553;
  }

  &:disabled {
    background-color: #e5e7eb;
    cursor: not-allowed;
  }
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>