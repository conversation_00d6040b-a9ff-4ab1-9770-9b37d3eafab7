<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <a href="https://agentq.id" class="text-2xl font-bold text-e94560">AgentQ</a>
          <div class="flex items-center space-x-4">
            <!-- Show Go to App button only for Enterprise users -->
            <button 
              v-if="profileData?.company?.subscription?.isEnterprise" 
              @click="goToApp" 
              class="header-button relative group"
            >
              <span class="material-icons mr-1">launch</span>
              Go to App
              
              <!-- Tooltip/guidance that appears on hover -->
              <div class="absolute hidden group-hover:block top-full right-0 mt-2 w-64 p-3 bg-blue-50 text-blue-800 text-sm rounded-md shadow-md border border-blue-200 z-10">
                <div class="flex items-start">
                  <span class="material-icons text-blue-500 mr-2 text-base">info</span>
                  <p>Click here to access the full Enterprise application with all features.</p>
                </div>
                <div class="absolute -top-2 right-4 w-4 h-4 bg-blue-50 border-t border-l border-blue-200 transform rotate-45"></div>
              </div>
            </button>
            <button @click="goToDashboard" class="header-button">
              <span class="material-icons mr-1">dashboard</span>
              Dashboard
            </button>
            <button @click="handleLogout" class="header-button">
              <span class="material-icons mr-1">logout</span>
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Enterprise User Guidance Banner -->
      <div 
        v-if="profileData?.company?.subscription?.isEnterprise && !dismissedGuidance" 
        class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 relative"
      >
        <button 
          @click="dismissedGuidance = true" 
          class="absolute top-2 right-2 text-blue-400 hover:text-blue-600"
          aria-label="Dismiss guidance"
        >
          <span class="material-icons">close</span>
        </button>
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <span class="material-icons text-blue-500 text-xl">tips_and_updates</span>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">
              Welcome to your Enterprise account!
            </h3>
            <div class="mt-2 text-sm text-blue-700">
              <p>As an Enterprise user, you have access to our full application with advanced features.</p>
              <p class="mt-1">Click the <span class="font-medium">"Go to App"</span> button in the header to access the complete Enterprise experience.</p>
            </div>
            <div class="mt-3">
              <button 
                @click="goToApp" 
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <span class="material-icons text-sm mr-1">launch</span>
                Go to App Now
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <h1 class="text-2xl font-bold mb-6">Settings</h1>
        
        <!-- Tabs -->
        <div class="border-b border-gray-200 mb-6">
          <nav class="flex -mb-px">
            <button 
              @click="changeTab('general')" 
              class="tab-button"
              :class="activeTab === 'general' ? 'tab-active' : 'tab-inactive'"
            >
              General Settings
            </button>
            <button 
              @click="changeTab('users')" 
              class="tab-button ml-8"
              :class="[
                activeTab === 'users' ? 'tab-active' : 'tab-inactive',
                !isEnterprise ? 'opacity-50 cursor-not-allowed' : ''
              ]"
              :disabled="!isEnterprise"
              :title="!isEnterprise ? 'Available only for Enterprise subscriptions' : ''"
            >
              User Management
            </button>
          </nav>
        </div>
        
        <!-- General Settings Tab -->
        <div v-if="activeTab === 'general'">
          <form @submit.prevent="updateCompanyName">
            <div class="mb-6">
              <label for="company-name" class="block text-sm font-medium text-gray-700 mb-1">
                Company Name
              </label>
              <input 
                type="text" 
                id="company-name" 
                v-model="companyName" 
                class="form-input w-full max-w-md"
                placeholder="Enter your company name"
              />
            </div>
            <div class="flex items-center">
              <button 
                type="submit" 
                class="primary-button"
                :disabled="loading || !companyName || companyName === profileData?.company?.name"
              >
                Save Changes
              </button>
              <span v-if="updateSuccess" class="ml-4 text-green-600">
                Company name updated successfully!
              </span>
            </div>
          </form>
        </div>
        
        <!-- User Management Tab -->
        <div v-else-if="activeTab === 'users'">
          <div v-if="!isEnterprise" class="bg-yellow-50 p-4 rounded-md mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <span class="material-icons text-yellow-400">warning</span>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">
                  Enterprise subscription required
                </h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>User management is only available for Enterprise subscriptions. Please upgrade your plan to access this feature.</p>
                </div>
                <div class="mt-4">
                  <button 
                    @click="showUpgradeModal = true" 
                    class="primary-button text-sm"
                  >
                    Upgrade Now
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else>
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-lg font-medium">Users ({{ users.length }}/20)</h2>
              <button 
                @click="openAddUserModal" 
                class="primary-button"
                :disabled="users.length >= 20"
              >
                <span class="material-icons mr-1">person_add</span>
                Add User
              </button>
            </div>
            
            <!-- Users Table -->
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-if="loadingUsers">
                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">Loading users...</td>
                  </tr>
                  <tr v-else-if="users.length === 0">
                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No users found</td>
                  </tr>
                  <tr v-for="user in users" :key="user.id" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <span class="text-gray-500 font-medium">{{ getInitials(user.name) }}</span>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ user.email }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ capitalizeFirstLetter(user.role) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                        :class="user.isEmailConfirmed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'">
                        {{ user.isEmailConfirmed ? 'Active' : 'Waiting Confirmation' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button 
                        v-if="user.role !== 'superadmin'"
                        @click="confirmDeleteUser(user)" 
                        class="text-red-600 hover:text-red-900"
                        :disabled="user.role === 'owner'"
                      >
                        Delete
                      </button>
                      <span v-if="user.role === 'superadmin'" class="text-gray-400">
                        No actions available
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Add/Edit User Modal -->
  <div v-if="showUserModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">Add New User</h3>
        <button @click="closeUserModal" class="text-gray-400 hover:text-gray-500">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <form @submit.prevent="saveUser">
        <div class="mb-4">
          <label for="user-name" class="block text-sm font-medium text-gray-700 mb-1">
            Name
          </label>
          <input 
            type="text" 
            id="user-name" 
            v-model="userForm.name" 
            class="form-input w-full"
            placeholder="Enter user name"
            required
          />
        </div>
        
        <div class="mb-4">
          <label for="user-email" class="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input 
            type="email" 
            id="user-email" 
            v-model="userForm.email" 
            class="form-input w-full"
            placeholder="Enter user email"
            required
          />
        </div>
        
        <div class="mb-4">
          <label for="user-password" class="block text-sm font-medium text-gray-700 mb-1">
            Password
          </label>
          <div class="flex">
            <input 
              type="password" 
              id="user-password" 
              v-model="userForm.password" 
              class="form-input w-full rounded-r-none"
              placeholder="Enter initial password"
              required
            />
            <button 
              type="button"
              @click="copyPassword"
              class="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200"
              title="Copy password"
            >
              <span class="material-icons text-gray-600">content_copy</span>
            </button>
          </div>
          <p class="text-xs text-gray-500 mt-1">
            Password must be at least 6 characters long.
          </p>
          <p v-if="passwordCopied" class="text-xs text-green-600 mt-1">
            Password copied to clipboard!
          </p>
        </div>
        
        <div class="mb-6">
          <label for="user-role" class="block text-sm font-medium text-gray-700 mb-1">
            Role
          </label>
          <select 
            id="user-role" 
            v-model="userForm.role" 
            class="form-input w-full"
            required
          >
            <option value="admin">Admin</option>
            <option value="user">User</option>
          </select>
        </div>
        
        <div class="flex justify-end">
          <button 
            type="button" 
            @click="closeUserModal" 
            class="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            type="submit" 
            class="primary-button"
            :disabled="userFormLoading || (!userForm.password || userForm.password.length < 6)"
          >
            Add User
          </button>
        </div>
      </form>
    </div>
  </div>
  
  <!-- Delete User Confirmation Modal -->
  <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">Delete User</h3>
        <button @click="showDeleteModal = false" class="text-gray-400 hover:text-gray-500">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <p class="mb-6 text-gray-700">
        Are you sure you want to delete the user <strong>{{ userToDelete?.name }}</strong>? This action cannot be undone.
      </p>
      
      <div class="flex justify-end">
        <button 
          type="button" 
          @click="showDeleteModal = false" 
          class="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Cancel
        </button>
        <button 
          @click="deleteUser" 
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
          :disabled="deleteLoading"
        >
          Delete
        </button>
      </div>
    </div>
  </div>
  
  <UpgradePlanModal 
    v-if="showUpgradeModal" 
    :currentSubscription="profileData?.company?.subscription" 
    @close="showUpgradeModal = false"
    @upgrade="handleUpgradeSuccess"
  />
  <!-- Add this notification component at the end of the template, before closing </div> -->
  <div 
    v-if="showNotification" 
    class="fixed bottom-4 right-4 p-4 rounded-md shadow-lg transition-all duration-300"
    :class="notificationType === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
  >
    <div class="flex items-center">
      <span 
        class="material-icons mr-2"
        :class="notificationType === 'success' ? 'text-green-500' : 'text-red-500'"
      >
        {{ notificationType === 'success' ? 'check_circle' : 'error' }}
      </span>
      <p>{{ notificationMessage }}</p>
      <button 
        @click="showNotification = false" 
        class="ml-4 text-gray-500 hover:text-gray-700"
      >
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import UpgradePlanModal from '../components/common/UpgradePlanModal.vue';
import axios from 'axios';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const profileData = ref<any>(null);
const loading = ref(false);
const activeTab = ref(route.query.tab as string || 'general');
const companyName = ref('');
const updateSuccess = ref(false);

// User management
const users = ref<any[]>([]);
const loadingUsers = ref(false);
const showUserModal = ref(false);
const userForm = ref({
  name: '',
  email: '',
  password: '',
  role: 'user'
});
const userFormLoading = ref(false);
const showDeleteModal = ref(false);
const userToDelete = ref<any>(null);
const deleteLoading = ref(false);
const showUpgradeModal = ref(false);
const passwordCopied = ref(false); // Add this line to define the passwordCopied ref

// Add these refs for notifications
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationType = ref<'success' | 'error'>('success');

const dismissedGuidance = ref(false);

const isEnterprise = computed(() => {
  return profileData.value?.company?.subscription?.isEnterprise === true;
});

const handleLogout = () => {
  authStore.logout();
  router.push('/login');
};

const goToDashboard = () => {
  router.push('/dashboard');
};

const goToApp = () => {
  // Pass the current auth token and company ID to the app URL
  const token = authStore.token;
  const companyId = profileData.value?.company?.id;
  const companyName = profileData.value?.company?.name;
  const companyUser = profileData.value?.company?.users[0];
  const companySubscription = profileData.value?.company?.subscription;
  
  // Encode company data to safely include in URL
  const companyData = encodeURIComponent(JSON.stringify({
    id: companyId,
    name: companyName,
    user: companyUser ? {
      id: companyUser.id,
      name: companyUser.name,
      email: companyUser.email,
      role: companyUser.role
    } : null,
    subscription: companySubscription ? {
      id: companySubscription.id,
      name: companySubscription.name,
      isEnterprise: companySubscription.isEnterprise
    } : null
  }));
  
  window.location.href = `${(import.meta as any).env.VITE_APP_URL || 'https://app.agentq.id'}/auth/external?token=${token}&company=${companyData}`;
};

const fetchProfileData = async () => {
  try {
    loading.value = true;
    const response = await fetch(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/profile`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });
    
    if (!response.ok) {
      if(response.status === 401){
        localStorage.removeItem('token');
        router.push('/login');
        return;
      }
      throw new Error('Failed to fetch profile data');
    }

    const data = await response.json();
    profileData.value = data;
    companyName.value = data.company?.name || ''; // Set company name from profile data
    
    // Log company details for debugging
    console.log('Company details:', {
      id: data.company?.id,
      name: data.company?.name,
      subscription: data.company?.subscription
    });
    
    // If enterprise, fetch users
    if (isEnterprise.value) {
      fetchUsers();
    }
    
    loading.value = false;
  } catch (error) {
    console.error('Error fetching profile data:', error);
    loading.value = false;
    router.push('/login');
  }
};

const updateCompanyName = async () => {
  try {
    loading.value = true;
    updateSuccess.value = false;
    
    console.log(`Updating company name to: ${companyName.value}`);
    
    // Use the profile/update-company endpoint which should be available
    const response = await axios.patch(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/profile/update-company`,
      { name: companyName.value },
      {
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      }
    );
    
    console.log('Updated company response:', response.data);
    
    // Update local data with response data to ensure consistency
    if (profileData.value && profileData.value.company) {
      profileData.value.company.company_name = companyName.value;
    }
    
    // Refresh profile data to ensure all data is in sync
    await fetchProfileData();
    
    updateSuccess.value = true;
    
    // Show success notification
    notificationMessage.value = 'Company name updated successfully!';
    notificationType.value = 'success';
    showNotification.value = true;
    
    // Hide notification after 3 seconds
    setTimeout(() => {
      showNotification.value = false;
    }, 3000);
    
    loading.value = false;
  } catch (error: any) {
    console.error('Error updating company name:', error);
    // Show error notification
    notificationMessage.value = `Error: ${error.response?.data?.message || error.message || 'Failed to update company name'}`;
    notificationType.value = 'error';
    showNotification.value = true;
    
    // Hide notification after 3 seconds
    setTimeout(() => {
      showNotification.value = false;
    }, 3000);
    
    loading.value = false;
  }
};

const fetchUsers = async () => {
  try {
    loadingUsers.value = true;
    
    // Make sure we have the company ID before making the request
    if (!profileData.value?.company?.id) {
      console.error('Company ID not available');
      loadingUsers.value = false;
      return;
    }
    
    // Update to fetch members instead of users
    const apiUrl = `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/companies/${profileData.value.company.id}/members`;
    console.log('Fetching members from:', apiUrl);
    
    const response = await fetch(apiUrl, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Error response:', response.status, errorData);
      throw new Error(`Failed to fetch members: ${response.status} ${errorData.message || ''}`);
    }
    
    const data = await response.json();
    users.value = data; // Still store in users array for display
    loadingUsers.value = false;
  } catch (error) {
    console.error('Error fetching members:', error);
    loadingUsers.value = false;
  }
};

const openAddUserModal = () => {
  // Reset form
  userForm.value = {
    name: '',
    email: '',
    password: '',
    role: 'user'
  };
  passwordCopied.value = false;
  showUserModal.value = true;
};

const closeUserModal = () => {
  showUserModal.value = false;
};

const saveUser = async () => {
  try {
    userFormLoading.value = true;
    
    // Add new member instead of user
    console.log('Adding new member:', userForm.value);
    console.log('Company ID:', profileData.value.company.id);
    
    const response = await fetch(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/companies/${profileData.value.company.id}/members`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: userForm.value.name,
        email: userForm.value.email,
        password: userForm.value.password,
        role: userForm.value.role
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Error response:', response.status, errorData);
      throw new Error(`Failed to add member: ${errorData.message || response.statusText}`);
    }
    
    const newMember = await response.json();
    console.log('New member added:', newMember);
    users.value.push(newMember); // Still add to users array for display
    
    closeUserModal();
    userFormLoading.value = false;
    
    // Show success notification
    notificationMessage.value = 'Member added successfully! An invitation email has been sent.';
    notificationType.value = 'success';
    showNotification.value = true;
    setTimeout(() => { showNotification.value = false; }, 3000);
  } catch (error: any) {
    console.error('Error saving member:', error);
    userFormLoading.value = false;
    
    // Show error notification
    notificationMessage.value = `Error: ${error.message}`;
    notificationType.value = 'error';
    showNotification.value = true;
    setTimeout(() => { showNotification.value = false; }, 3000);
  }
};

const confirmDeleteUser = (user: any) => {
  userToDelete.value = user;
  showDeleteModal.value = true;
};

const deleteUser = async () => {
  try {
    deleteLoading.value = true;
    
    // Use the companies endpoint instead of users endpoint
    const response = await fetch(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/companies/${profileData.value.company.id}/users/${userToDelete.value.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Error response:', response.status, errorData);
      throw new Error(`Failed to delete user: ${errorData.message || response.statusText}`);
    }
    
    // Remove from local data
    users.value = users.value.filter(u => u.id !== userToDelete.value.id);
    
    showDeleteModal.value = false;
    userToDelete.value = null;
    deleteLoading.value = false;
    
    // Show success notification
    notificationMessage.value = 'User deleted successfully!';
    notificationType.value = 'success';
    showNotification.value = true;
    setTimeout(() => { showNotification.value = false; }, 3000);
  } catch (error: any) {
    console.error('Error deleting user:', error);
    deleteLoading.value = false;
    
    // Show error notification
    notificationMessage.value = `Error: ${error.message}`;
    notificationType.value = 'error';
    showNotification.value = true;
    setTimeout(() => { showNotification.value = false; }, 3000);
  }
};

const getInitials = (name: string) => {
  if (!name) return '';
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

const capitalizeFirstLetter = (string: string) => {
  if (!string) return '';
  return string.charAt(0).toUpperCase() + string.slice(1);
};

const handleUpgradeSuccess = () => {
  showUpgradeModal.value = false;
  // Refresh profile data to get updated subscription info
  fetchProfileData();
};

const copyPassword = async () => {
  try {
    await navigator.clipboard.writeText(userForm.value.password);
    passwordCopied.value = true;
    
    // Hide the "copied" message after 3 seconds
    setTimeout(() => {
      passwordCopied.value = false;
    }, 3000);
  } catch (err) {
    console.error('Failed to copy password:', err);
    
    // Show error notification
    notificationMessage.value = 'Failed to copy password to clipboard';
    notificationType.value = 'error';
    showNotification.value = true;
    setTimeout(() => { showNotification.value = false; }, 3000);
  }
};

const changeTab = (tab: string) => {
  activeTab.value = tab;
  router.replace({ query: { ...route.query, tab } });
};

onMounted(() => {
  fetchProfileData();
});
</script>

<style scoped>
.header-button {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  transition: all 0.2s;
}

.header-button:hover {
  background-color: #f3f4f6;
}

.primary-button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  background-color: #e94560;
  transition: all 0.2s;
}

.primary-button:hover:not(:disabled) {
  background-color: #d03050;
}

.primary-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #e94560;
  box-shadow: 0 0 0 3px rgba(233, 69, 96, 0.1);
}

.tab-button {
  padding: 0.5rem 0;
  font-weight: 500;
  font-size: 0.875rem;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-active {
  color: #e94560;
  border-bottom-color: #e94560;
}

.tab-inactive {
  color: #6b7280;
}

.tab-inactive:hover:not(:disabled) {
  color: #4b5563;
}
</style>
