<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h1 class="mt-6 text-center logo">AgentQ</h1>
    </div>
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Email Confirmation
      </h2>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div v-if="isLoading" class="text-center">
          <div class="spinner mb-4"></div>
          <p class="text-gray-600">Confirming your email...</p>
        </div>

        <div v-else-if="error" class="text-center">
          <div class="text-red-600 mb-4">
            <span class="material-icons text-5xl">error_outline</span>
          </div>
          <h3 class="text-lg font-medium text-red-800 mb-2">Confirmation Failed</h3>
          <p class="text-gray-600 mb-6">{{ error }}</p>
          <button 
            @click="goToApp" 
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Go to Login
          </button>
        </div>

        <div v-else class="text-center">
          <div class="text-green-600 mb-4">
            <span class="material-icons text-5xl">check_circle</span>
          </div>
          <h3 class="text-lg font-medium text-green-800 mb-2">Email Confirmed!</h3>
          <p class="text-gray-600 mb-6">Your email has been successfully confirmed.</p>
          <p class="text-gray-600 mb-6">Redirecting you to the app...</p>
          <div v-if="!redirecting">
            <button 
              @click="goToApp" 
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mb-3"
            >
              Go to App
            </button>
            <button 
              @click="goToApp" 
              class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Go to Login
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

const API_URL = `${(import.meta as any).env.VITE_CORE_SERVICE_URL}`;
const APP_URL = `${(import.meta as any).env.VITE_APP_URL}`;
const isLoading = ref(true);
const error = ref('');
const confirmedEmail = ref('');
const redirecting = ref(false);
const redirectUrl = ref('');

onMounted(async () => {
  const token = new URLSearchParams(window.location.search).get('token');
  const redirect = new URLSearchParams(window.location.search).get('redirect');
  
  if (!token) {
    error.value = 'Invalid confirmation link. The token is missing.';
    isLoading.value = false;
    return;
  }

  try {
    const response = await fetch(`${API_URL}/auth/confirm?token=${token}`);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to confirm email');
    }

    confirmedEmail.value = data.email || '';
    redirectUrl.value = data.redirectUrl || '';
    isLoading.value = false;
    
    // If we have a redirect URL from the backend or from the query param, redirect after a short delay
    if (redirectUrl.value || redirect === 'app') {
      redirecting.value = true;
      setTimeout(() => {
        goToApp();
      }, 2000);
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to confirm email';
    isLoading.value = false;
  }
});

const goToApp = () => {
  // If we have a specific redirect URL from the backend, use it
  if (redirectUrl.value) {
    window.location.href = redirectUrl.value;
  } 
  // Otherwise use the default app URL from env
  else {
    window.location.href = APP_URL || 'https://app.agentq.id';
  }
};
</script>

<style scoped>
.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #4f46e5;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.logo {
  font-size: 28px;
  font-weight: bold;
  color: #e94560;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
