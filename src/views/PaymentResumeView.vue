<template>
  <div class="payment-resume">
    <div class="container">
      <div class="card">
        <div v-if="loading" class="loading">
          <div class="spinner"></div>
          <p>Loading payment details...</p>
        </div>
        
        <div v-else-if="error" class="error">
          <span class="material-icons">error</span>
          <h2>Payment Issue</h2>
          <p>{{ error }}</p>
          <button class="primary-button" @click="goToDashboard">Return to Dashboard</button>
        </div>
        
        <div v-else class="payment-details">
          <h2>Resume Payment</h2>
          
          <div class="info-row">
            <span class="label">Order ID:</span>
            <span class="value">{{ orderId }}</span>
          </div>
          
          <div class="info-row" v-if="paymentDetails">
            <span class="label">Amount:</span>
            <span class="value">Rp {{ formatAmount(paymentDetails.amount) }}</span>
          </div>
          
          <div class="info-row" v-if="paymentDetails">
            <span class="label">Plan:</span>
            <span class="value">{{ formatPlanName(paymentDetails.plan_id) }}</span>
          </div>
          
          <div class="info-row" v-if="paymentDetails">
            <span class="label">Status:</span>
            <span class="value" :class="getStatusClass(paymentDetails.status)">
              {{ paymentDetails.status }}
            </span>
          </div>
          
          <div class="actions">
            <button class="primary-button" @click="resumePayment">
              Continue Payment
            </button>
            <button class="secondary-button" @click="goToDashboard">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { useAuthStore } from '../stores/auth';

const router = useRouter();
const authStore = useAuthStore();
const loading = ref(true);
const error = ref('');
const orderId = ref('');
const paymentDetails = ref<any>(null);

onMounted(async () => {
  try {
    // Get order ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    orderId.value = urlParams.get('order_id') || '';
    
    if (!orderId.value) {
      throw new Error('Missing order ID');
    }
    
    // Get payment details from backend
    const response = await axios.get(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/details/${orderId.value}`,
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    if (!response.data) {
      throw new Error('Payment not found');
    }
    
    paymentDetails.value = response.data;
    
    // If payment is already completed, redirect to finish page
    if (paymentDetails.value.status === 'settlement' || 
        paymentDetails.value.status === 'capture') {
      router.push(`/payment/finish?order_id=${orderId.value}&transaction_status=${paymentDetails.value.status}`);
      return;
    }
    
    loading.value = false;
  } catch (err) {
    console.error('Error loading payment details:', err);
    error.value = (err as Error).message || 'Failed to load payment details';
    loading.value = false;
  }
});

const resumePayment = async () => {
  try {
    loading.value = true;
    
    // Get payment redirect URL
    if (paymentDetails.value.invoice_url) {
      window.location.href = paymentDetails.value.invoice_url;
    } else {
      // If no redirect URL, try to recreate the transaction
      const response = await axios.post(
        `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/resume/${orderId.value}`,
        {},
        {
          headers: {
            'Authorization': authStore.getAuthHeader
          }
        }
      );
      
      if (response.data && response.data.redirect_url) {
        window.location.href = response.data.redirect_url;
      } else {
        throw new Error('Failed to resume payment');
      }
    }
  } catch (err) {
    console.error('Error resuming payment:', err);
    error.value = (err as Error).message || 'Failed to resume payment';
    loading.value = false;
  }
};

const goToDashboard = () => {
  router.push('/dashboard');
};

const formatAmount = (amount: number) => {
  return amount.toLocaleString('id-ID');
};

const formatPlanName = (planId: string) => {
  if (planId === 'enterprise') {
    return 'Enterprise Plan';
  } else if (planId === 'topup') {
    const quantity = paymentDetails.value.quantity || 1;
    return `Token Top-up (${quantity}M tokens)`;
  }
  return planId;
};

const getStatusClass = (status: string) => {
  switch (status.toLowerCase()) {
    case 'paid':
    case 'settlement':
    case 'capture':
      return 'status-success';
    case 'pending':
      return 'status-pending';
    case 'failed':
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'status-failed';
    default:
      return '';
  }
};
</script>

<style scoped>
.payment-resume {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 500px;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 40px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: #e94560;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.error {
  text-align: center;
}

.error .material-icons {
  font-size: 48px;
  color: #ef4444;
  margin-bottom: 16px;
}

.payment-details h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #1f2937;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.label {
  font-weight: 500;
  color: #6b7280;
}

.value {
  font-weight: 600;
  color: #1f2937;
}

.status-success {
  color: #10b981;
}

.status-pending {
  color: #f59e0b;
}

.status-failed {
  color: #ef4444;
}

.actions {
  margin-top: 32px;
  display: flex;
  justify-content: space-between;
}

.primary-button, .secondary-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.primary-button {
  background-color: #e94560;
  color: white;
}

.primary-button:hover {
  background-color: #d63553;
}

.secondary-button {
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
}

.secondary-button:hover {
  background-color: #e2e8f0;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
