<template>
    <div class="login-container">
      <div class="login-header">
        <h1 class="logo">AgentQ</h1>
      </div>
      
      <div class="login-card">
        <h2 class="login-title">Reset your password</h2>
        <p class="login-subtitle">Enter your email address and we'll send you a link to reset your password</p>
        
        <form @submit.prevent="handleSubmit" class="login-form">
          <div class="form-group">
            <label for="email">Email address</label>
            <input 
              type="email" 
              id="email" 
              v-model="email" 
              required
              placeholder="Enter your email"
              @input="validateEmail"
            />
            <p v-if="emailError" class="error-message">{{ emailError }}</p>
          </div>
          
          <div v-if="error" class="error-message">
            {{ error }}
          </div>
  
          <div v-if="success" class="success-message">
            {{ success }}
          </div>
          
          <button 
            type="submit" 
            class="login-button"
            :disabled="isLoading || !!emailError"
          >
            <span v-if="isLoading" class="loading-spinner"></span>
            <span v-else>Send Reset Link</span>
          </button>
  
          <div class="text-center mt-6">
            <router-link to="/login" class="back-link">
              <span class="material-icons align-middle text-sm mr-1">arrow_back</span>
              Back to Login
            </router-link>
          </div>
        </form>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  import { useAuthStore } from '../stores/auth'
  
  const authStore = useAuthStore()
  const email = ref('')
  const emailError = ref('')
  const error = ref('')
  const success = ref('')
  const isLoading = ref(false)
  
  const validateEmail = () => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    emailError.value = emailPattern.test(email.value) ? '' : 'Invalid email format'
  }
  
  async function handleSubmit() {
    error.value = ''
    success.value = ''
    isLoading.value = true
  
    try {
      const response = await authStore.forgotPassword(email.value)
      success.value = response.message
      email.value = '' // Clear the form
    } catch (err: any) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: #f5f7fa;
    padding: 20px;
  }
  
  .login-header {
    margin-bottom: 30px;
    text-align: center;
  }
  
  .logo {
    font-size: 28px;
    font-weight: bold;
    color: #e94560;
  }
  
  .login-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 40px;
    width: 100%;
    max-width: 450px;
  }
  
  .login-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    text-align: center;
  }
  
  .login-subtitle {
    color: #6b7280;
    text-align: center;
    margin-bottom: 24px;
  }
  
  .login-form {
    display: flex;
    flex-direction: column;
  }
  
  .form-group {
    margin-bottom: 16px;
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #374151;
    }
    
    input {
      width: 100%;
      padding: 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      font-size: 16px;
      
      &:focus {
        outline: none;
        border-color: #e94560;
        box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
      }
    }
  }
  
  .error-message {
    background-color: #fee2e2;
    color: #b91c1c;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
  }
  
  .success-message {
    background-color: #dcfce7;
    color: #15803d;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
  }
  
  .login-button {
    background-color: #e94560;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover:not(:disabled) {
      background-color: #d63553;
    }
  
    &:disabled {
      background-color: #e5e7eb;
      cursor: not-allowed;
    }
  }
  
  .back-link {
    color: #e94560;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  </style>