<template>
  <div class="login-container">
    <div class="login-header">
      <h1 class="logo">AgentQ</h1>
    </div>
    
    <div class="login-card">
      <div v-if="isLoading" class="text-center">
        <div class="loading-spinner mx-auto mb-4"></div>
        <p class="text-gray-600">Confirming your email...</p>
      </div>
      
      <div v-else-if="error" class="text-center">
        <span class="material-icons text-red-500 text-5xl mb-4">error</span>
        <h2 class="text-xl font-semibold text-gray-800 mb-2">Email Confirmation Failed</h2>
        <br>
        <p class="text-gray-600 mb-6">{{ error }}</p>
        <br>
        <button 
          @click="router.push('/login')" 
          class="login-button"
        >
          Back to Login
        </button>
      </div>
      
      <div v-else class="text-center">
        <span class="material-icons text-green-500 text-5xl mb-4">check_circle</span>
        <h2 class="text-xl font-semibold text-gray-800 mb-2">Email Confirmed!</h2>
        <p class="text-gray-600 mb-6">Your email has been successfully confirmed. You can now log in to your account.</p>
        <button 
          @click="router.push('/login')" 
          class="login-button"
        >
          Continue to Login
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const API_URL = `${(import.meta as any).env.VITE_CORE_SERVICE_URL}`
const router = useRouter()
const isLoading = ref(true)
const error = ref('')

onMounted(async () => {
  const token = new URLSearchParams(window.location.search).get('token')
  
  if (!token) {
    error.value = 'Invalid confirmation link'
    isLoading.value = false
    return
  }

  try {
    const response = await fetch(`${API_URL}/auth/confirm?token=${token}`)
    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.message || 'Failed to confirm email')
    }

    isLoading.value = false
  } catch (err: any) {
    error.value = err.message || 'Failed to confirm email'
    isLoading.value = false
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.login-header {
  margin-bottom: 30px;
  text-align: center;
}

.logo {
  font-size: 28px;
  font-weight: bold;
  color: #e94560;
}

.login-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 40px;
  width: 100%;
  max-width: 450px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: #e94560;
  animation: spin 1s linear infinite;
}

.login-button {
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  
  &:hover {
    background-color: #d63553;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>