<template>
  <div class="payment-history-page bg-gray-100 min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
      <div class="bg-black rounded-lg shadow-sm p-6 text-white">
        <h1 class="text-2xl font-bold mb-1">Payments</h1>
        <p class="text-gray-400 mb-6">Review your payment history</p>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-700">
            <thead>
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-400">Date</th>
                <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-400">Amount</th>
                <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-400">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-400">Invoice</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-700">
              <tr v-if="loading">
                <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-400">Loading payment history...</td>
              </tr>
              <tr v-else-if="payments.length === 0">
                <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-400">No payment history found</td>
              </tr>
              <tr v-for="payment in payments" :key="payment.id" class="hover:bg-gray-800">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                  {{ formatDate(payment.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                  Rp {{ formatAmount(payment.amount) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <span :class="getStatusClass(payment.status)">
                    {{ formatStatus(payment.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <a 
                    v-if="payment.status === 'settlement' || payment.status === 'capture' || payment.status === 'paid'" 
                    @click.prevent="viewInvoiceDetails(payment.order_id)"
                    href="#"
                    class="text-green-500 hover:text-green-400 cursor-pointer"
                  >
                    View
                  </a>
                  <span v-else-if="payment.status === 'pending'">
                    <button 
                      @click="checkPaymentStatus(payment.order_id)"
                      class="text-blue-500 hover:text-blue-400 mr-3"
                    >
                      Check
                    </button>
                    <button 
                      @click="openPaymentPage(payment.order_id)"
                      class="text-green-500 hover:text-green-400"
                    >
                      Pay Now
                    </button>
                  </span>
                  <span v-else>-</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="flex justify-between items-center mt-6 text-sm">
          <div class="text-gray-400">
            Page {{ currentPage }} of {{ totalPages }}
          </div>
          <div>
            <button 
              @click="loadPage(currentPage - 1)" 
              :disabled="currentPage === 1"
              class="px-3 py-1 border border-gray-600 rounded mr-2 text-gray-300"
              :class="currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-800'"
            >
              Previous
            </button>
            <button 
              @click="loadPage(currentPage + 1)" 
              :disabled="currentPage === totalPages"
              class="px-3 py-1 border border-gray-600 rounded text-gray-300"
              :class="currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-800'"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { useAuthStore } from '../stores/auth';

const router = useRouter();
const authStore = useAuthStore();
const payments = ref<any[]>([]);
const loading = ref(true);
const currentPage = ref(1);
const totalPages = ref(1);
const pageSize = 10;
let checkInterval: number | null = null;

onMounted(() => {
  loadPayments();
  
  // Check for pending payments every 60 seconds
  checkInterval = window.setInterval(() => {
    checkPendingPayments();
  }, 60000);
});

onUnmounted(() => {
  if (checkInterval) {
    clearInterval(checkInterval);
  }
});

const loadPayments = async () => {
  try {
    loading.value = true;
    const response = await axios.get(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/history?page=${currentPage.value}&limit=${pageSize}`,
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    payments.value = response.data.items;
    totalPages.value = response.data.meta.totalPages;
    loading.value = false;
  } catch (error) {
    console.error('Error loading payment history:', error);
    loading.value = false;
  }
};

const loadPage = (page: number) => {
  if (page < 1 || page > totalPages.value) return;
  currentPage.value = page;
  loadPayments();
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  });
};

const formatAmount = (amount: number) => {
  return amount.toLocaleString('id-ID');
};

const formatStatus = (status: string) => {
  switch (status.toLowerCase()) {
    case 'settlement':
    case 'capture':
      return 'Paid';
    case 'refund':
      return 'Refunded';
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'Failed';
    default:
      return status.charAt(0).toUpperCase() + status.slice(1);
  }
};

const getStatusClass = (status: string) => {
  switch (status.toLowerCase()) {
    case 'paid':
    case 'settlement':
    case 'capture':
      return 'text-green-500';
    case 'pending':
      return 'text-yellow-500';
    case 'failed':
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'text-red-500';
    case 'refund':
    case 'refunded':
      return 'text-blue-500';
    default:
      return 'text-gray-400';
  }
};

const checkPaymentStatus = async (orderId: string) => {
  try {
    loading.value = true;
    const response = await axios.get(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/check-status/${orderId}`,
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    if (response.data.status === 'success') {
      // Refresh user profile to get updated subscription info
      await authStore.checkAuth();
      // Reload payments to show updated status
      loadPayments();
    }
    
    loading.value = false;
  } catch (error) {
    console.error('Error checking payment status:', error);
    loading.value = false;
  }
};

const openPaymentPage = (orderId: string) => {
  router.push(`/payment/resume?order_id=${orderId}`);
};

const viewInvoiceDetails = (orderId: string) => {
  router.push(`/invoice/details?order_id=${orderId}`);
};

const checkPendingPayments = async () => {
  try {
    // Find all pending payments
    const pendingPayments = payments.value.filter(p => 
      p.status.toLowerCase() === 'pending'
    );
    
    // Check status for each pending payment
    for (const payment of pendingPayments) {
      await checkPaymentStatus(payment.order_id);
    }
  } catch (error) {
    console.error('Error checking pending payments:', error);
  }
};
</script>
