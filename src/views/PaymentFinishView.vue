<template>
  <div class="payment-finish">
    <div class="container">
      <div class="card">
        <div v-if="loading" class="loading">
          <div class="spinner"></div>
          <p>Processing your payment...</p>
        </div>
        
        <div v-else-if="error" class="error">
          <span class="material-icons">error</span>
          <h2>Payment Issue</h2>
          <p>{{ error }}</p>
          <button class="secondary-button" @click="manuallyUpdateSubscription" v-if="orderId">
            Try Manual Update
          </button>
          <button class="primary-button" @click="goToDashboard">Return to Dashboard</button>
        </div>
        
        <div v-else class="success">
          <span class="material-icons">check_circle</span>
          <h2>Payment Successful!</h2>
          <p>Your subscription has been updated successfully.</p>
          <button class="primary-button" @click="goToDashboard">Go to Dashboard</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { useAuthStore } from '../stores/auth';

const router = useRouter();
const authStore = useAuthStore();
const loading = ref(true);
const error = ref('');
const orderId = ref('');

onMounted(async () => {
  try {
    // Check URL parameters for payment status and order ID
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('transaction_status');
    orderId.value = urlParams.get('order_id') || '';
    
    console.log(`Payment finish page loaded. Status: ${status}, Order ID: ${orderId.value}`);
    
    if (!orderId.value) {
      throw new Error('Missing order ID');
    }
    
    // Immediately check the payment status with the backend
    const response = await axios.get(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/check-status/${orderId.value}`,
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    console.log('Initial payment status check:', response.data);
    
    if (response.data.status === 'success') {
      // Refresh user profile to get updated subscription info
      await authStore.checkAuth();
      loading.value = false;
      return;
    }
    
    // If not immediately successful, poll the payment status
    let attempts = 0;
    const maxAttempts = 10;
    const pollInterval = 3000; // 3 seconds
    
    const checkPaymentStatus = async () => {
      try {
        const response = await axios.get(
          `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/check-status/${orderId.value}`,
          {
            headers: {
              'Authorization': authStore.getAuthHeader
            }
          }
        );
        
        console.log(`Payment status check attempt ${attempts + 1}:`, response.data);
        
        if (response.data.status === 'success') {
          // Refresh user profile to get updated subscription info
          await authStore.checkAuth();
          loading.value = false;
          return true;
        } else if (response.data.status === 'pending' && attempts < maxAttempts) {
          // Try again after interval
          attempts++;
          setTimeout(checkPaymentStatus, pollInterval);
          return false;
        } else if (response.data.status === 'pending') {
          // Max attempts reached but still pending
          error.value = 'Payment is still being processed. Please check your dashboard later or try manual update.';
          loading.value = false;
          return false;
        } else {
          error.value = response.data.message || 'Payment processing failed';
          loading.value = false;
          return false;
        }
      } catch (err) {
        console.error('Error checking payment status:', err);
        if (attempts < maxAttempts) {
          attempts++;
          setTimeout(checkPaymentStatus, pollInterval);
          return false;
        } else {
          error.value = (err as Error).message || 'Failed to verify payment status';
          loading.value = false;
          return false;
        }
      }
    };
    
    // Start polling
    checkPaymentStatus();
  } catch (err) {
    console.error('Error in payment finish page:', err);
    error.value = (err as Error).message || 'Failed to process payment';
    loading.value = false;
  }
});

const goToDashboard = () => {
  router.push('/dashboard');
};

const manuallyUpdateSubscription = async () => {
  try {
    loading.value = true;
    error.value = '';
    
    const response = await axios.post(
      `${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/manual-update/${orderId.value}`,
      {},
      {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      }
    );
    
    console.log('Manual update response:', response.data);
    
    if (response.data.status === 'success') {
      // Refresh user profile to get updated subscription info
      await authStore.checkAuth();
      loading.value = false;
    } else {
      error.value = response.data.message || 'Manual update failed. Payment may not be completed.';
      loading.value = false;
    }
  } catch (err) {
    console.error('Error during manual update:', err);
    error.value = (err as Error).message || 'Failed to perform manual update';
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.payment-finish {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 500px;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 40px;
  text-align: center;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-radius: 50%;
    border-top-color: #e94560;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  p {
    color: #6b7280;
  }
}

.error, .success {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .material-icons {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1f2937;
  }
  
  p {
    color: #6b7280;
    margin-bottom: 24px;
  }
}

.error .material-icons {
  color: #ef4444;
}

.success .material-icons {
  color: #10b981;
}

.primary-button, .secondary-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  margin: 8px;
}

.primary-button {
  background-color: #e94560;
  color: white;
  
  &:hover {
    background-color: #d63553;
  }
}

.secondary-button {
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  
  &:hover {
    background-color: #e2e8f0;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
