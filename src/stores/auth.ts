import { defineStore } from 'pinia'
import axios from 'axios'
import router from '../router'

const API_URL = `${(import.meta as any).env.VITE_CORE_SERVICE_URL}`

interface SignupData {
  name: string
  email: string
  company?: string
  password: string
}

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    user: JSON.parse(localStorage.getItem('user') || 'null'),
    refreshToken: localStorage.getItem('refreshToken') || '',
    tokenExpiry: localStorage.getItem('tokenExpiry') ? parseInt(localStorage.getItem('tokenExpiry') || '0') : 0
  }),
  
  getters: {
    isAuthenticated: (state) => !!state.token,
    getAuthHeader: (state) => `Bearer ${state.token}`
  },
  
  actions: {
    setToken(token: string, refreshToken: string, expiresIn: number) {
      this.token = token;
      this.refreshToken = refreshToken;
      
      // Calculate token expiry time (current time + expires_in in seconds)
      const expiryTime = Date.now() + (expiresIn * 1000);
      this.tokenExpiry = expiryTime;
      
      // Store in localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('refreshToken', refreshToken);
      localStorage.setItem('tokenExpiry', expiryTime.toString());
    },
    
    setUser(user: any) {
      this.user = user;
      localStorage.setItem('user', JSON.stringify(user));
    },
    
    async signup(data: SignupData) {
      try {
        const response = await fetch(`${API_URL}/auth/signup`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.message || 'Signup failed')
        }

        const responseData = await response.json()
        return responseData
      } catch (error) {
        console.error('Signup error:', error)
        throw error
      }
    },

    async login(email: string, password: string) {
      try {
        const response = await axios.post(
          `${API_URL}/auth/login`,
          { email, password }
        );
        
        const { access_token, refresh_token, expires_in, user } = response.data;
        
        this.setToken(access_token, refresh_token, expires_in);
        this.setUser(user);
        
        return { success: true };
      } catch (error: any) {
        console.error('Login error:', error);
        // Extract the specific error message from the response
        const errorMessage = error.response?.data?.message || 'Login failed';
        
        return { 
          success: false, 
          message: errorMessage
        };
      }
    },

    async forgotPassword(email: string) {
      try {
        const response = await fetch(`${API_URL}/auth/forgot-password`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email }),
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.message || 'Failed to send reset password email')
        }

        return await response.json()
      } catch (error) {
        console.error('Forgot password error:', error)
        throw error
      }
    },

    async resetPassword(token: string, password: string) {
      try {
        const response = await fetch(`${API_URL}/auth/reset-password`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token, password }),
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.message || 'Failed to reset password')
        }

        return await response.json()
      } catch (error) {
        console.error('Reset password error:', error)
        throw error
      }
    },

    async handleGithubCallback(params: URLSearchParams) {
      try {
        const tokenParam = params.get('token');
        const userParam = params.get('user');
        
        if (!tokenParam || !userParam) {
          throw new Error('Missing token or user data in callback parameters');
        }
        
        // Parse the user data
        const userData = JSON.parse(userParam);
        
        // Set the token and user in the store
        this.token = tokenParam;
        this.user = userData;
        
        // Store in localStorage
        localStorage.setItem('token', tokenParam);
        localStorage.setItem('user', userParam);
        
        return true;
      } catch (error) {
        console.error('GitHub callback error:', error);
        throw error;
      }
    },

    async refreshAuthToken() {
      try {
        // Only attempt to refresh if we have a refresh token
        if (!this.refreshToken) {
          throw new Error('No refresh token available');
        }
        
        const response = await axios.post(
          `${API_URL}/auth/refresh`,
          { refresh_token: this.refreshToken }
        );
        
        const { access_token, refresh_token, expires_in } = response.data;
        
        this.setToken(access_token, refresh_token || this.refreshToken, expires_in);
        
        return true;
      } catch (error) {
        console.error('Token refresh failed:', error);
        // If refresh fails, log the user out
        this.logout();
        return false;
      }
    },

    async checkTokenExpiry() {
      // If token is expired or about to expire (within 5 minutes), refresh it
      const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
      
      if (this.tokenExpiry && this.tokenExpiry < fiveMinutesFromNow) {
        console.log('Token is expiring soon, refreshing...');
        return this.refreshAuthToken();
      }
      
      return true;
    },

    async checkAuth() {
      try {
        // First check if token needs refreshing
        await this.checkTokenExpiry();
        
        // Then verify with the backend
        const response = await axios.get(
          `${API_URL}/profile`,
          {
            headers: {
              'Authorization': this.getAuthHeader
            }
          }
        );
        
        // Update user data
        this.setUser(response.data);
        
        return true;
      } catch (error) {
        console.error('Auth check failed:', error);
        return false;
      }
    },

    logout() {
      this.token = '';
      this.user = null;
      this.refreshToken = '';
      this.tokenExpiry = 0;
      
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('tokenExpiry');
      
      router.push('/login');
    }
  }
})
