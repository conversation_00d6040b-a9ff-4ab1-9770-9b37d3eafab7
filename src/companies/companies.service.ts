import { Injectable, NotFoundException, ForbiddenException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, EntityManager } from 'typeorm';
import { Company } from '../users/entities/company.entity';
import { User } from '../users/entities/user.entity';
import { MemberRole } from '../users/entities/member.entity';
import * as bcrypt from 'bcrypt';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { ConfigService } from '@nestjs/config';
import { ResendService } from '../mailer/resend.service';
import * as crypto from 'crypto';
import { Member } from '../users/entities/member.entity';

@Injectable()
export class CompaniesService {
  constructor(
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Member)
    private membersRepository: Repository<Member>,
    private configService: ConfigService,
    private resendService: ResendService,
    private entityManager: EntityManager,
  ) {}

  async findAll(): Promise<Company[]> {
    return this.companiesRepository.find({
      relations: ['users'] // Remove 'subscription' from relations
    });
  }

  async findOne(id: string): Promise<Company> {
    console.log(`Finding company with ID: ${id}`);
    
    const company = await this.companiesRepository.findOne({
      where: { id },
      relations: ['users'] // Remove 'subscription' from relations
    });
    
    if (!company) {
      console.log(`Company with ID ${id} not found`);
      throw new NotFoundException(`Company with ID ${id} not found`);
    }

    console.log(`Found company: ${company.company_name}`);
    return company;
  }

  async update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<Company> {
    console.log(`Updating company with ID: ${id}`);
    console.log(`Update data:`, updateCompanyDto);
    
    const company = await this.findOne(id);
    
    // Update company properties
    if (updateCompanyDto.name) {
      company.company_name = updateCompanyDto.name; // Use company_name instead of name
    }
    
    const updatedCompany = await this.companiesRepository.save(company);
    console.log(`Company updated: ${updatedCompany.company_name}`);
    
    return updatedCompany;
  }

  async saveCompany(company: Company): Promise<Company> {
    return this.companiesRepository.save(company);
  }

  async addMemberToCompany(companyId: string, memberData: { name: string; email: string; role: string; password?: string }) {
    console.log(`Adding member to company ${companyId}:`, memberData);
    
    const company = await this.companiesRepository.findOne({
      where: { id: companyId },
      relations: ['members']
    });
    
    if (!company) {
      console.log(`Company with ID ${companyId} not found`);
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }
    
    // Check if member with this email already exists
    const existingMember = await this.membersRepository.findOne({
      where: { email: memberData.email, companyId: companyId }
    });
    
    if (existingMember) {
      console.log(`Member with email ${memberData.email} already exists in this company`);
      throw new BadRequestException(`Member with email ${memberData.email} already exists in this company`);
    }
    
    // Create a new member with the provided data
    const member = new Member();
    member.name = memberData.name;
    member.email = memberData.email;
    member.role = memberData.role as MemberRole;
    member.isEmailConfirmed = false; // Set to false until confirmed
    member.companyId = companyId;
    
    // Generate confirmation token
    const confirmationToken = this.generateToken();
    console.log(`Generated confirmation token for ${memberData.email}: ${confirmationToken}`);
    member.confirmationToken = confirmationToken;
    member.confirmationTokenExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    
    // Use provided password or generate a random one
    let tempPassword: string;
    if (memberData.password) {
      tempPassword = memberData.password;
    } else {
      tempPassword = Math.random().toString(36).slice(-8);
    }
    
    // Store the original password in a separate field for later use
    member.originalPassword = tempPassword;
    
    const salt = await bcrypt.genSalt();
    member.originalPassword = await bcrypt.hash(tempPassword, salt);
    
    try {
      // Save the member
      console.log('Saving new member:', member);
      const savedMember = await this.membersRepository.save(member);
      console.log('Member saved successfully:', savedMember.id);
      
      // Send invitation email
      await this.sendMemberInvitationEmail(savedMember, tempPassword, company);
      
      return {
        id: savedMember.id,
        name: savedMember.name,
        email: savedMember.email,
        role: savedMember.role,
        companyId: savedMember.companyId
      };
    } catch (error) {
      console.error('Error saving member:', error);
      throw new InternalServerErrorException(`Failed to create member: ${error.message}`);
    }
  }

  async removeUserFromCompany(companyId: string, userId: string) {
    console.log(`Removing user ${userId} from company ${companyId}`);
    
    const company = await this.companiesRepository.findOne({
      where: { id: companyId },
      relations: ['users', 'members']
    });
    
    if (!company) {
      console.log(`Company with ID ${companyId} not found`);
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }
    
    // First check if it's a regular user in the members table
    const member = await this.membersRepository.findOne({
      where: { id: userId, companyId: companyId }
    });
    
    if (member) {
      try {
        // Remove the member
        await this.membersRepository.remove(member);
        return {
          success: true,
          message: 'Member removed successfully'
        };
      } catch (error) {
        console.error('Error removing member:', error);
        throw new InternalServerErrorException(`Failed to remove member: ${error.message}`);
      }
    }
    
    // If not found in members table, check users table (for superadmin)
    const user = await this.usersRepository.findOne({
      where: { id: userId, company: { id: companyId } }
    });
    
    if (!user) {
      console.log(`User/Member with ID ${userId} not found in company ${companyId}`);
      throw new NotFoundException(`User/Member with ID ${userId} not found in company ${companyId}`);
    }
    
    // Check if user is the superadmin
    if (user.role === 'superadmin') {
      throw new ForbiddenException('Cannot remove the superadmin');
    }
    
    try {
      // Remove the user
      await this.usersRepository.remove(user);
      
      return {
        success: true,
        message: 'User removed successfully'
      };
    } catch (error) {
      console.error('Error removing user:', error);
      throw new InternalServerErrorException(`Failed to remove user: ${error.message}`);
    }
  }

  private generateToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  private async sendMemberInvitationEmail(member: Member, tempPassword: string, company: Company) {
    try {
      const frontendUrl = this.configService.get('FRONTEND_URL');
      const agentqApp = this.configService.get('AGENTQ_APP_URL');
      const confirmationUrl = `${frontendUrl}/confirm-email-member-invitation?token=${member.confirmationToken}&password=${tempPassword}&redirect=app`;
      
      console.log(`Sending invitation email to ${member.email} for company ${company.company_name}`);
      
      await this.resendService.sendTemplateEmail({
        to: member.email,
        subject: `You've been invited to join ${company.company_name} on AgentQ App`,
        template: 'invitation-agentq-app',
        data: {
          name: member.name,
          companyName: company.company_name,
          companyId: company.id,
          confirmationUrl,
          email: member.email,
          password: tempPassword,
          role: member.role,
          appUrl: agentqApp
        }
      });
      
      console.log(`Invitation email sent successfully to ${member.email}`);
    } catch (error) {
      console.error('Failed to send invitation email:', error);
      // Don't throw here, just log the error
    }
  }

  async getCompanyMembers(companyId: string) {
    const members = await this.membersRepository.find({
      where: { companyId },
      order: { createdAt: 'DESC' }
    });
    
    return members.map(member => ({
      id: member.id,
      name: member.name,
      email: member.email,
      role: member.role,
      isEmailConfirmed: member.isEmailConfirmed
    }));
  }

  async getMemberById(id: string) {
    return this.membersRepository.findOne({ where: { id } });
  }

  async confirmMemberEmail(token: string, password: string) {
    console.log(`Attempting to confirm member email with token: ${token}`);
    
    try {
      // Use the entity manager directly
      const member = await this.entityManager.findOne(Member, {
        where: { confirmationToken: token }
      });
      
      if (!member) {
        console.log(`No member found with token: ${token}`);
        throw new BadRequestException('Invalid confirmation token');
      }
      
      console.log(`Found member: ${member.email}`);
      
      if (member.confirmationTokenExpires < new Date()) {
        console.log(`Token expired for member: ${member.email}`);
        throw new BadRequestException('Confirmation token has expired');
      }
      
      // Update using entity manager
      member.isEmailConfirmed = true;
      member.confirmationToken = null;
      member.confirmationTokenExpires = null;
      
      await this.entityManager.save(Member, member);
      console.log(`Member updated using EntityManager`);

      // Register the member in the enterprise database via API
      try {
        const enterpriseApiUrl = this.configService.get('AGENTQ_APP_API_URL');
        console.log(`Registering member in enterprise database at ${enterpriseApiUrl}`);
        
        const response = await fetch(`${enterpriseApiUrl}/auth/register`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            email: member.email,
            password: password,
            name: member.name,
            role: member.role,
            companyId: member.companyId
          })
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('Failed to register member in enterprise database:', errorData);
          // Continue even if enterprise registration fails
        } else {
          const data = await response.json();
          console.log('Member registered in enterprise database:', data.user.id);
        }
      } catch (error) {
        console.error('Error calling enterprise register API:', error);
        // Continue even if enterprise registration fails
      }
      
      // Get the app URL for redirection
      const appUrl = this.configService.get('AGENTQ_APP_URL');
      
      return { 
        message: 'Email confirmed successfully',
        email: member.email,
        redirectUrl: appUrl || null,
        companyId: member.companyId || null
      };
    } catch (error) {
      console.error(`Error in confirmMemberEmail: ${error.message}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(`Unexpected error: ${error.message}`);
    }
  }
}
