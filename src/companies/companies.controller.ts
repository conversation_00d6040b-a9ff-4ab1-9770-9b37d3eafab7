import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Req, NotFoundException, ForbiddenException, BadRequestException, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CompaniesService } from './companies.service';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { AddUserDto } from './dto/add-user.dto';
import { Public } from '../auth/public.decorator';
import { UsersService } from '../users/user.service';
import { ConfigService } from '@nestjs/config';

@ApiTags('companies')
@Controller('companies')
export class CompaniesController {
  constructor(
    private readonly companiesService: CompaniesService,
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
  ) {}

  // Add this method at the top of the class to make it more visible
  @Public() // Add this decorator if available, or create it
  @Get('confirm-email-member-invitation')
  @ApiOperation({ summary: 'Confirm member email invitation' })
  @ApiResponse({ status: 200, description: 'Email invitation confirmed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid or expired token' })
  async confirmEmailMemberInvitation(@Query('token') token: string, @Query('password') password: string) {
    console.log(`Controller received token: "${token}" and password: "${password}"`);
    
    if (!token) {
      throw new BadRequestException('Confirmation token is required');
    }
    
    // Trim the token in case there are any whitespace issues
    const trimmedToken = token.trim();
    if (trimmedToken !== token) {
      console.log(`Token had whitespace, trimmed from "${token}" to "${trimmedToken}"`);
    }

    if (!password) {
      throw new BadRequestException('Confirmation password is required');
    }
    
    try {
      return await this.companiesService.confirmMemberEmail(trimmedToken, password);
    } catch (error) {
      console.error(`Controller error handling token ${trimmedToken}:`, error);
      throw error;
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get company by ID' })
  async findOne(@Param('id') id: string, @Req() req: any) {
    const company = await this.companiesService.findOne(id);
    
    if (!company) {
      throw new NotFoundException('Company not found');
    }
    
    // Check if user belongs to this company
    const user = req.user;
    const userBelongsToCompany = company.users.some(companyUser => companyUser.id === user.id);
    
    if (!userBelongsToCompany) {
      throw new ForbiddenException('You do not have permission to view this company');
    }
    
    return company;
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update company details' })
  async updateCompany(
    @Param('id') id: string,
    @Body() updateCompanyDto: UpdateCompanyDto,
    @Req() req: any
  ) {
    console.log(`Updating company ${id} with data:`, updateCompanyDto);
    
    // Check if user has permission to update this company
    const user = req.user;
    const company = await this.companiesService.findOne(id);
    
    if (!company) {
      throw new NotFoundException('Company not found');
    }
    
    // Check if user belongs to this company
    const userBelongsToCompany = company.users.some(companyUser => companyUser.id === user.id);
    
    if (!userBelongsToCompany) {
      throw new ForbiddenException('You do not have permission to update this company');
    }
    
    return this.companiesService.update(id, updateCompanyDto);
  }

  @Get(':id/users')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all users for a company' })
  async getCompanyUsers(
    @Param('id') id: string,
    @Req() req: any
  ) {
    // Check if user has permission to view this company's users
    const user = req.user;
    const company = await this.companiesService.findOne(id);
    
    if (!company) {
      throw new NotFoundException('Company not found');
    }
    
    // Check if user belongs to this company
    const userBelongsToCompany = company.users.some(companyUser => companyUser.id === user.id);
    
    if (!userBelongsToCompany) {
      throw new ForbiddenException('You do not have permission to view this company\'s users');
    }
    
    return company.users;
  }

  @Post(':id/update-name')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update company name' })
  async updateCompanyName(
    @Param('id') id: string,
    @Body() body: { name: string },
    @Req() req: any
  ) {
    console.log(`Updating company ${id} name to:`, body.name);
    
    // Check if user has permission to update this company
    const user = req.user;
    const company = await this.companiesService.findOne(id);
    
    if (!company) {
      throw new NotFoundException('Company not found');
    }
    
    // Check if user belongs to this company
    const userBelongsToCompany = company.users.some(companyUser => companyUser.id === user.id);
    
    if (!userBelongsToCompany) {
      throw new ForbiddenException('You do not have permission to update this company');
    }
    
    // Update company name directly
    company.company_name = body.name;
    const updatedCompany = await this.companiesService.saveCompany(company);
    
    return {
      success: true,
      message: 'Company name updated successfully',
      company: {
        id: updatedCompany.id,
        name: updatedCompany.company_name
      }
    };
  }

  @Post(':id/members')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Add a member to a company' })
  async addMemberToCompany(
    @Param('id') id: string,
    @Body() memberData: AddUserDto, // You can reuse the same DTO or create a new one
    @Req() req: any
  ) {
    console.log(`Request to add member to company ${id}:`, memberData);
    
    try {
      // Check if user has permission to add members to this company
      const user = req.user;
      console.log('Requesting user:', user.id);

      // Check if the email address already exists in the users table
      const existingUser = await this.usersService.findOne(memberData.email);
      if (existingUser) {
        throw new BadRequestException(`User with email ${memberData.email} already exists. Please use a different email address.`);
      }
      
      const company = await this.companiesService.findOne(id);
      
      if (!company) {
        throw new NotFoundException('Company not found');
      }
      
      // Check if user belongs to this company
      const userBelongsToCompany = company.users.some(companyUser => companyUser.id === user.id);
      
      if (!userBelongsToCompany) {
        throw new ForbiddenException('You do not have permission to add members to this company');
      }
      
      const result = await this.companiesService.addMemberToCompany(id, memberData);
      console.log('Member added successfully:', result);
      return result;
    } catch (error) {
      console.error('Error adding member to company:', error);
      throw error;
    }
  }

  @Get(':id/members')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all members of a company' })
  async getCompanyMembers(@Param('id') id: string, @Req() req: any) {
    // Check if user has permission to view this company's members
    const user = req.user;
    const company = await this.companiesService.findOne(id);
    
    if (!company) {
      throw new NotFoundException('Company not found');
    }
    
    // Check if user belongs to this company
    const userBelongsToCompany = company.users.some(companyUser => companyUser.id === user.id);
    
    if (!userBelongsToCompany) {
      throw new ForbiddenException('You do not have permission to view this company\'s members');
    }
    
    return this.companiesService.getCompanyMembers(id);
  }

  @Post(':id/test')
  @ApiOperation({ summary: 'Test endpoint' })
  async testEndpoint(@Param('id') id: string) {
    return { message: 'Test endpoint working', id };
  }

  @Delete(':id/users/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Remove a user from a company' })
  async removeUserFromCompany(
    @Param('id') id: string,
    @Param('userId') userId: string,
    @Req() req: any
  ) {
    console.log(`Request to remove user ${userId} from company ${id}`);
    
    try {
      // Check if user has permission to remove users from this company
      const user = req.user;
      console.log('Requesting user:', user.id);
      
      const company = await this.companiesService.findOne(id);
      
      if (!company) {
        throw new NotFoundException('Company not found');
      }
      
      // Check if user belongs to this company
      const userBelongsToCompany = company.users.some(companyUser => companyUser.id === user.id);
      
      if (!userBelongsToCompany) {
        throw new ForbiddenException('You do not have permission to remove users from this company');
      }
      
      // Check if the user being removed is the superadmin
      const userToRemove = company.users.find(u => u.id === userId);
      if (userToRemove && userToRemove.role === 'superadmin') {
        throw new ForbiddenException('Cannot remove the company owner');
      }
      
      // Get user email for deletion from auth service
      const memberToRemove = await this.companiesService.getMemberById(userId);
      if (memberToRemove && memberToRemove.email) {
        try {
          const enterpriseApiUrl = this.configService.get('AGENTQ_APP_API_URL');
          const jwtSecret = this.configService.get('JWT_SECRET');
          
          // Delete user from auth service
          const response = await fetch(`${enterpriseApiUrl}/auth/users/${encodeURIComponent(memberToRemove.email)}`, {
            method: 'DELETE',
            headers: {
              'accept': '*/*',
              'x-api-key': jwtSecret
            }
          });
          
          if (!response.ok) {
            console.warn(`Failed to delete user from auth service: ${response.status}`);
          } else {
            console.log(`User deleted from auth service: ${memberToRemove.email}`);
          }
        } catch (error) {
          console.error('Error deleting user from auth service:', error);
          // Continue with local deletion even if auth service deletion fails
        }
      }
      
      const result = await this.companiesService.removeUserFromCompany(id, userId);
      console.log('User removed successfully:', result);
      return result;
    } catch (error) {
      console.error('Error removing user from company:', error);
      throw error;
    }
  }
}
