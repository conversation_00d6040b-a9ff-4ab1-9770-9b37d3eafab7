import { En<PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Subscription } from '../../subscriptions/entities/subscription.entity';

@Entity('companies')
export class Company {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  company_name: string;

  @Column({ nullable: true })
  token: string;

  @Column({ nullable: true })
  userId: string;

  @OneToMany(() => User, user => user.company)
  users: User[];

  @ManyToOne(() => Subscription, { nullable: true })
  @JoinColumn()
  subscription: Subscription;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
