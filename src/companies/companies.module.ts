import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompaniesController } from './companies.controller';
import { CompaniesService } from './companies.service';
import { Company } from '../users/entities/company.entity';
import { User } from '../users/entities/user.entity';
import { Member } from '../users/entities/member.entity';
import { ResendService } from '../mailer/resend.service';
import { UsersService } from '../users/user.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Company, User, Member]),
  ],
  controllers: [CompaniesController],
  providers: [
    CompaniesService,
    ResendService,
    UsersService,
  ],
  exports: [CompaniesService],
})
export class CompaniesModule {}
