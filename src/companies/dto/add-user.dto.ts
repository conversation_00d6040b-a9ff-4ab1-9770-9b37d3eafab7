import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Option<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class AddUserDto {
  @ApiProperty({
    description: 'User name',
    example: '<PERSON>'
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>'
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'User password',
    example: 'password123'
  })
  @IsString()
  @MinLength(6)
  @IsOptional() // Make it optional to maintain backward compatibility
  password?: string;

  @ApiProperty({
    description: 'User role',
    example: 'user'
  })
  @IsString()
  @IsNotEmpty()
  role: string;
}
