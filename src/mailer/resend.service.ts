import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Resend } from 'resend';

@Injectable()
export class ResendService {
  private resend: Resend;
  
  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('RESEND_API_KEY');
    if (!apiKey) {
      console.warn('RESEND_API_KEY is not set. Email functionality will not work.');
    }
    this.resend = new Resend(apiKey);
  }
  
  async sendEmail({
    to,
    subject,
    html,
    text,
    from,
    replyTo,
    attachments,
  }: {
    to: string | string[];
    subject: string;
    html?: string;
    text?: string;
    from?: string;
    replyTo?: string;
    attachments?: Array<{ filename: string; content: Buffer }>;
  }) {
    try {
      const defaultFrom = this.configService.get<string>('RESEND_FROM_EMAIL', '<EMAIL>');
      const fromName = this.configService.get<string>('RESEND_FROM_NAME', 'AgentQ');
      
      const result = await this.resend.emails.send({
        from: from || `${fromName} <${defaultFrom}>`,
        to: Array.isArray(to) ? to : [to],
        subject,
        html,
        text,
        replyTo, // Changed from reply_to to replyTo
        attachments: attachments?.map(attachment => ({
          filename: attachment.filename,
          content: attachment.content.toString('base64'),
        })),
      });
      
      // The response structure has data property that contains the id
      console.log('Email sent successfully:', result?.data?.id);
      return result;
    } catch (error) {
      console.error('Failed to send email with Resend:', error);
      throw error;
    }
  }
  
  async sendTemplateEmail({
    to,
    subject,
    template,
    data,
    from,
    replyTo,
  }: {
    to: string | string[];
    subject: string;
    template: string;
    data: Record<string, any>;
    from?: string;
    replyTo?: string;
  }) {
    try {
      // Simple template rendering
      let html = '';
      
      if (template === 'confirmation') {
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>Welcome to AgentQ!</h2>
            <p>Hi ${data.name},</p>
            <p>Thank you for signing up. Please confirm your email address by clicking the button below:</p>
            
            <a href="${data.confirmationUrl}" style="display: inline-block; padding: 12px 24px; background-color: #e94560; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0;">Confirm Email</a>
            
            <p>This confirmation link will expire in 24 hours.</p>
            
            <p>If you didn't create an account, you can safely ignore this email.</p>
            
            <div style="margin-top: 30px; font-size: 14px; color: #666;">
              <p>Best regards,<br>The AgentQ Team</p>
            </div>
          </div>
        `;
      } else if (template === 'reset-password') {
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>Reset Your Password</h2>
            <p>Hi ${data.name},</p>
            <p>We received a request to reset your password. Click the button below to choose a new password:</p>
            
            <a href="${data.resetUrl}" style="display: inline-block; padding: 12px 24px; background-color: #e94560; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0;">Reset Password</a>
            
            <p>This link will expire in 1 hour.</p>
            
            <p>If you didn't request a password reset, you can safely ignore this email.</p>
            
            <div style="margin-top: 30px; font-size: 14px; color: #666;">
              <p>Best regards,<br>The AgentQ Team</p>
            </div>
          </div>
        `;
      } else if (template === 'invitation-agentq-app') {
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>You've Been Invited to Join ${data.companyName} on AgentQ App!</h2>
            <p>Hi ${data.name},</p>
            <p>You've been invited to join <strong>${data.companyName}</strong> on AgentQ as a <strong>${data.role}</strong>.</p>
            
            <div style="background-color: #f8f9fa; border-radius: 6px; padding: 16px; margin: 20px 0;">
              <p style="margin: 0 0 8px 0;"><strong>Your login details:</strong></p>
              <p style="margin: 0 0 4px 0;">Email: ${data.email}</p>
              <p style="margin: 0;">Password: ${data.password}</p>
            </div>
            
            <p>To get started, please confirm your email address by clicking the button below:</p>
            
            <a href="${data.confirmationUrl}" style="display: inline-block; padding: 12px 24px; background-color: #e94560; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0;">Confirm Email & Log In</a>
            
            <p>This confirmation link will expire in 24 hours. After confirming your email, you can log in with the password provided above.</p>
            
            <p>For security reasons, we recommend regularly renewing your password.</p>
            
            <div style="margin-top: 30px; font-size: 14px; color: #666;">
              <p>Best regards,<br>The AgentQ Team</p>
            </div>
          </div>
        `;
      }
      
      return this.sendEmail({
        to,
        subject,
        html,
        from,
        replyTo,
      });
    } catch (error) {
      console.error('Failed to send template email with Resend:', error);
      throw error;
    }
  }
}
