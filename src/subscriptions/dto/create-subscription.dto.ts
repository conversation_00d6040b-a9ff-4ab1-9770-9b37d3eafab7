import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateSubscriptionDto {
  @ApiProperty({ description: 'Subscription name', example: 'Enterprise' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Token limit', example: 500000 })
  @IsNumber()
  @IsNotEmpty()
  token_limit: number;

  @ApiPropertyOptional({ description: 'Price per billing cycle', example: 99.99 })
  @IsNumber()
  @IsOptional()
  price?: number;

  @ApiProperty({ description: 'Billing cycle interval', example: '1 month' })
  @IsString()
  @IsNotEmpty()
  billing_cycle: string;
}