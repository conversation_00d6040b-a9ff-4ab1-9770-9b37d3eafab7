import { <PERSON>, Get, Post, Body, Param, UseGuards, Inject } from '@nestjs/common';
import { SubscriptionsService } from './subscriptions.service';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';

@ApiTags('subscriptions')
@Controller('subscriptions')
export class SubscriptionsController {
  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new subscription plan' })
  @ApiResponse({ status: 201, description: 'The subscription has been successfully created.' })
  create(@Body() createSubscriptionDto: CreateSubscriptionDto) {
    return this.subscriptionsService.create(createSubscriptionDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all subscription plans' })
  @ApiResponse({ status: 200, description: 'Return all subscription plans.' })
  findAll() {
    return this.subscriptionsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a subscription plan by ID' })
  @ApiResponse({ status: 200, description: 'Return the subscription plan.' })
  @ApiResponse({ status: 404, description: 'Subscription plan not found.' })
  findOne(@Param('id') id: string) {
    return this.subscriptionsService.findOne(id);
  }

  @Get('debug/company/:companyId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Debug company subscriptions' })
  @ApiResponse({ status: 200, description: 'Return all subscriptions for a company.' })
  async debugCompanySubscriptions(@Param('companyId') companyId: string) {
    try {
      // Get all subscriptions for the company
      const allSubscriptions = await this.subscriptionsService.getAllCompanySubscriptions(companyId);
      
      // Get the current active subscription
      const activeSubscription = await this.subscriptionsService.getCompanySubscription(companyId);
      
      return {
        allSubscriptions,
        activeSubscription,
        now: new Date()
      };
    } catch (error) {
      return {
        error: error.message,
        stack: error.stack
      };
    }
  }

  @Post('fix-enterprise-subscription')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Fix enterprise subscription for a user' })
  @ApiResponse({ status: 200, description: 'Subscription fixed successfully.' })
  async fixEnterpriseSubscription(@Body() body: { userId: string, endDate?: string }) {
    try {
      const { userId, endDate } = body;
      
      // Get user with company
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['company']
      });
      
      if (!user || !user.company) {
        return {
          success: false,
          message: 'User or company not found'
        };
      }
      
      const companyId = user.company.id;
      
      // Get the enterprise subscription plan
      const enterpriseSubscription = await this.subscriptionsService.getSubscriptionByName('Enterprise');
      
      if (!enterpriseSubscription) {
        return {
          success: false,
          message: 'Enterprise subscription plan not found'
        };
      }
      
      // Create a new enterprise subscription
      const subscriptionEndDate = endDate ? new Date(endDate) : new Date();
      if (!endDate) {
        subscriptionEndDate.setFullYear(subscriptionEndDate.getFullYear() + 1); // Default to 1 year
      }
      
      const newSubscription = await this.subscriptionsService.createCompanySubscription({
        companyId,
        subscriptionId: enterpriseSubscription.id,
        start_date: new Date(),
        end_date: subscriptionEndDate,
        remaining_tokens: enterpriseSubscription.token_limit
      });
      
      return {
        success: true,
        message: 'Created new enterprise subscription',
        subscription: newSubscription
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        stack: error.stack
      };
    }
  }

  @Post('manual-fix/:companyId')
  @ApiOperation({ summary: 'Manually fix subscription for a company' })
  @ApiResponse({ status: 200, description: 'Subscription fixed successfully.' })
  async manualFixSubscription(
    @Param('companyId') companyId: string,
    @Body() body: { endDate?: string, tokenLimit?: number }
  ) {
    try {
      const { endDate, tokenLimit } = body;
      
      // Get the enterprise subscription plan
      const enterpriseSubscription = await this.subscriptionsService.getSubscriptionByName('Enterprise');
      
      if (!enterpriseSubscription) {
        return {
          success: false,
          message: 'Enterprise subscription plan not found'
        };
      }
      
      // Create a new enterprise subscription
      const subscriptionEndDate = endDate ? new Date(endDate) : new Date();
      if (!endDate) {
        subscriptionEndDate.setFullYear(subscriptionEndDate.getFullYear() + 1); // Default to 1 year
      }
      
      const newSubscription = await this.subscriptionsService.createCompanySubscription({
        companyId,
        subscriptionId: enterpriseSubscription.id,
        start_date: new Date(),
        end_date: subscriptionEndDate,
        remaining_tokens: tokenLimit || enterpriseSubscription.token_limit,
      });
      
      return {
        success: true,
        message: 'Created new enterprise subscription',
        subscription: newSubscription
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        stack: error.stack
      };
    }
  }

  @Post('fix-enterprise/:companyId')
  @ApiOperation({ summary: 'Fix enterprise subscription for a company' })
  @ApiResponse({ status: 200, description: 'Subscription fixed successfully.' })
  async fixEnterpriseSubscriptionForCompany(@Param('companyId') companyId: string) {
    try {
      // Get the enterprise subscription plan
      const enterpriseSubscription = await this.subscriptionsService.getSubscriptionByName('Enterprise');
      
      if (!enterpriseSubscription) {
        return {
          success: false,
          message: 'Enterprise subscription plan not found'
        };
      }
      
      // Get all company subscriptions
      const allSubscriptions = await this.subscriptionsService.getAllCompanySubscriptions(companyId);
      console.log('All company subscriptions:', allSubscriptions);
      
      // Find the latest subscription (which might be Free)
      const latestSubscription = allSubscriptions[0];
      
      if (!latestSubscription) {
        return {
          success: false,
          message: 'No subscription found for company'
        };
      }
      
      // Create a new enterprise subscription
      const endDate = new Date();
      endDate.setFullYear(endDate.getFullYear() + 1); // 12 months
      
      const newSubscription = await this.subscriptionsService.createCompanySubscription({
        companyId,
        subscriptionId: enterpriseSubscription.id,
        start_date: new Date(),
        end_date: endDate,
        remaining_tokens: enterpriseSubscription.token_limit
      });
      
      return {
        success: true,
        message: 'Created new enterprise subscription',
        oldSubscription: latestSubscription,
        newSubscription
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        stack: error.stack
      };
    }
  }

  @Get('status/:companyId')
  @ApiOperation({ summary: 'Get subscription status for a company' })
  @ApiResponse({ status: 200, description: 'Return subscription status.' })
  async getSubscriptionStatus(@Param('companyId') companyId: string) {
    try {
      // Get all subscriptions for the company
      const allSubscriptions = await this.subscriptionsService.getAllCompanySubscriptions(companyId);
      
      // Get the current active subscription
      const activeSubscription = await this.subscriptionsService.getCompanySubscription(companyId);
      
      // Get subscription plans
      const subscriptionPlans = await this.subscriptionsService.findAll();
      
      return {
        allSubscriptions,
        activeSubscription,
        subscriptionPlans,
        now: new Date()
      };
    } catch (error) {
      return {
        error: error.message,
        stack: error.stack
      };
    }
  }
}
