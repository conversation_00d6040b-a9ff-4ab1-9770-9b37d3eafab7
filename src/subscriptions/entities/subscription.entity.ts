import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { CompanySubscription } from './company-subscription.entity';

@Entity('subscriptions')
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  token_limit: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  price: number;

  @Column('interval')
  billing_cycle: string;

  @OneToMany(() => CompanySubscription, companySubscription => companySubscription.subscription)
  companySubscriptions: CompanySubscription[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}