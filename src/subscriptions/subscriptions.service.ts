import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan, <PERSON>Than } from 'typeorm';
import { Subscription } from './entities/subscription.entity';
import { CompanySubscription } from './entities/company-subscription.entity';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { createClient } from 'redis';
import { ConfigService } from '@nestjs/config';
import { Cron } from '@nestjs/schedule';
import { calculateSubscriptionEndDate } from '../utils/date.utils';

@Injectable()
export class SubscriptionsService {
  private readonly redisClient;

  constructor(
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    @InjectRepository(CompanySubscription)
    private companySubscriptionRepository: Repository<CompanySubscription>,
    private configService: ConfigService,
  ) {
    this.redisClient = createClient({
      url: this.configService.get('REDIS_URL'),
      password: this.configService.get('REDIS_PASSWORD'),
    });

    this.redisClient.on('connect', () => {
      console.log('Redis client connected');
    });
    this.redisClient.on('error', (err) => {
      console.error('Redis client error:', err);
    });
    this.redisClient.connect().then(async () => {
      const dbIndex = Number(this.configService.get('REDIS_DB'));
      await this.redisClient.select(dbIndex);
      console.log(`Switched Redis client to database ${dbIndex}`);
    }).catch((err) => {
      console.error('Error connecting to Redis or switching database:', err);
    });
  }

  async create(createSubscriptionDto: CreateSubscriptionDto): Promise<Subscription> {
    const subscription = this.subscriptionRepository.create(createSubscriptionDto);
    return this.subscriptionRepository.save(subscription);
  }

  async findAll(): Promise<Subscription[]> {
    return this.subscriptionRepository.find();
  }

  async findOne(id: string): Promise<Subscription> {
    const subscription = await this.subscriptionRepository.findOne({ where: { id } });
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }
    return subscription;
  }

  async assignFreeSubscriptionToCompany(companyId: string): Promise<CompanySubscription> {
    // Find the free subscription
    const freeSubscription = await this.subscriptionRepository.findOne({
      where: { name: 'Free' }
    });

    if (!freeSubscription) {
      throw new NotFoundException('Free subscription plan not found');
    }

    // Check if company already has a free subscription
    const existingFreeSubscription = await this.companySubscriptionRepository.findOne({
      where: { 
        companyId: companyId,
        subscription: { name: 'Free' }
      },
      relations: ['subscription']
    });

    if (existingFreeSubscription) {
      // If they already have a free subscription, just reset the tokens
      existingFreeSubscription.remaining_tokens = freeSubscription.token_limit;
      return this.companySubscriptionRepository.save(existingFreeSubscription);
    }

    // Create company subscription with no end date for free plan
    const companySubscription = this.companySubscriptionRepository.create({
      companyId: companyId,
      subscriptionId: freeSubscription.id,
      start_date: new Date(),
      end_date: null, // Free subscription never expires
      remaining_tokens: freeSubscription.token_limit, // Initialize with full token limit
    });

    return this.companySubscriptionRepository.save(companySubscription);
  }

  @Cron('0 0 1 * *') // Run at midnight on the first day of each month
  async resetMonthlyTokens() {
    try {
      console.log('Running monthly token reset job');
      
      // Get all active subscriptions
      const activeSubscriptions = await this.companySubscriptionRepository.find({
        where: { is_active: true },
        relations: ['subscription']
      });
      
      console.log(`Found ${activeSubscriptions.length} active subscriptions to process`);
      
      // Reset tokens for each subscription based on plan type
      for (const subscription of activeSubscriptions) {
        // Get the plan name
        const planName = subscription.subscription.name;
        
        if (planName.toLowerCase() === 'free') {
          // For Free plans, reset to the token limit
          console.log(`Resetting tokens for Free plan (Company ID: ${subscription.companyId})`);
          subscription.remaining_tokens = subscription.subscription.token_limit;
        } else if (planName.toLowerCase() === 'enterprise') {
          // For Enterprise plans, add the token limit to remaining tokens
          console.log(`Adding tokens for Enterprise plan (Company ID: ${subscription.companyId})`);
          subscription.remaining_tokens += subscription.subscription.token_limit;
        }
        
        await this.companySubscriptionRepository.save(subscription);
        
        // Store reset event in Redis for audit
        await this.redisClient.hSet(
          `token_reset:${subscription.companyId}`,
          new Date().toISOString(),
          JSON.stringify({
            planName: planName,
            tokenLimit: subscription.subscription.token_limit,
            newRemainingTokens: subscription.remaining_tokens,
            action: planName.toLowerCase() === 'free' ? 'reset' : 'add'
          })
        );
      }
      
      console.log('Monthly token reset completed successfully');
    } catch (error) {
      console.error('Error resetting monthly tokens:', error);
    }
  }

  async deductTokens(companyId: string, tokensToDeduct: number): Promise<void> {
    const companySubscription = await this.companySubscriptionRepository.findOne({
      where: { companyId: companyId },
      relations: ['subscription'],
      order: { created_at: 'DESC' }
    });

    if (!companySubscription) {
      throw new NotFoundException('No active subscription found');
    }

    if (companySubscription.remaining_tokens < tokensToDeduct) {
      throw new Error('Insufficient tokens');
    }

    companySubscription.remaining_tokens -= tokensToDeduct;
    await this.companySubscriptionRepository.save(companySubscription);

    // Log token deduction in Redis
    await this.redisClient.hSet(
      `token_usage:${companyId}`,
      new Date().toISOString(),
      tokensToDeduct.toString()
    );
  }

  async upgradeToEnterprise(companyId: string, duration: number = 12, validUntil?: Date, shouldAddTokens: boolean = true): Promise<CompanySubscription> {
    // Find the Enterprise subscription plan
    const enterpriseSubscription = await this.subscriptionRepository.findOne({
      where: { name: 'Enterprise' }
    });
    
    if (!enterpriseSubscription) {
      throw new NotFoundException('Enterprise subscription plan not found');
    }
    
    // Check if there's already an Enterprise subscription for this company
    let companySubscription = await this.companySubscriptionRepository.findOne({
      where: {
        companyId: companyId,
        subscription: { name: 'Enterprise' }
      },
      relations: ['subscription']
    });
    
    // Calculate end date
    const endDate = validUntil || new Date();
    if (!validUntil) {
      endDate.setMonth(endDate.getMonth() + duration);
    }
    
    if (companySubscription) {
      // Update existing subscription
      companySubscription.end_date = endDate;
      
      // FIXED: For subscription extensions, preserve the current token balance
      // Only set tokens to default value for new subscriptions
      if (!companySubscription.remaining_tokens) {
        companySubscription.remaining_tokens = enterpriseSubscription.token_limit;
        console.log(`Set tokens to ${enterpriseSubscription.token_limit} for company ${companyId}`);
      } else {
        console.log(`Preserving current token balance of ${companySubscription.remaining_tokens} for company ${companyId}`);
      }
      
      companySubscription.is_active = true;
      
      return this.companySubscriptionRepository.save(companySubscription);
    } else {
      // Create new subscription
      const newSubscription = this.companySubscriptionRepository.create({
        companyId: companyId,
        subscriptionId: enterpriseSubscription.id,
        start_date: new Date(),
        end_date: endDate,
        remaining_tokens: enterpriseSubscription.token_limit,
        is_active: true
      });
      
      return this.companySubscriptionRepository.save(newSubscription);
    }
  }
  
  async topUpTokens(companyId: string, tokenAmount: number): Promise<CompanySubscription> {
    const companySubscription = await this.companySubscriptionRepository.findOne({
      where: { companyId: companyId },
      order: { created_at: 'DESC' }
    });
  
    if (!companySubscription) {
      throw new NotFoundException('No active subscription found');
    }
  
    companySubscription.remaining_tokens += tokenAmount;
    return this.companySubscriptionRepository.save(companySubscription);
  }

  async getCompanySubscription(companyId: string): Promise<CompanySubscription | null> {
    try {
      console.log(`Getting subscription for company ${companyId}`);
      
      // First check for active Enterprise subscription
      const enterpriseSubscription = await this.companySubscriptionRepository.findOne({
        where: { 
          companyId: companyId,
          subscription: { name: 'Enterprise' },
          is_active: true
        },
        relations: ['subscription'],
        order: { created_at: 'DESC' }
      });
      
      if (enterpriseSubscription) {
        console.log(`Found active enterprise subscription for company ${companyId}`, enterpriseSubscription);
        return enterpriseSubscription;
      }
      
      // If no active enterprise subscription, check for any enterprise subscription
      // that might not be marked as active but has a valid end_date
      const now = new Date();
      const validEnterpriseSubscription = await this.companySubscriptionRepository.findOne({
        where: { 
          companyId: companyId,
          subscription: { name: 'Enterprise' }
        },
        relations: ['subscription'],
        order: { created_at: 'DESC' }
      });
      
      if (validEnterpriseSubscription && 
          validEnterpriseSubscription.end_date && 
          validEnterpriseSubscription.end_date > now) {
        console.log(`Found valid enterprise subscription for company ${companyId}`, validEnterpriseSubscription);
        return validEnterpriseSubscription;
      }
      
      // If no active enterprise subscription, get the free subscription
      const freeSubscription = await this.companySubscriptionRepository.findOne({
        where: { 
          companyId: companyId,
          subscription: { name: 'Free' }
        },
        relations: ['subscription'],
        order: { created_at: 'DESC' }
      });
      
      if (freeSubscription) {
        console.log(`Found free subscription for company ${companyId}`, freeSubscription);
        return freeSubscription;
      }
      
      // If no subscription found at all, return null
      console.log(`No subscription found for company ${companyId}`);
      return null;
    } catch (error) {
      console.error('Error getting company subscription:', error);
      return null;
    }
  }

  @Cron('0 0 * * *') // Run at midnight every day
  async checkExpiredSubscriptions() {
    try {
      console.log('Checking for expired subscriptions...');
      
      // Find all expired enterprise subscriptions
      const expiredSubscriptions = await this.companySubscriptionRepository.find({
        where: {
          end_date: LessThan(new Date()),
          subscription: { name: 'Enterprise' }
        },
        relations: ['subscription']
      });
      
      console.log(`Found ${expiredSubscriptions.length} expired enterprise subscriptions`);
      
      // Process each expired subscription
      for (const subscription of expiredSubscriptions) {
        console.log(`Processing expired subscription for company ${subscription.companyId}`);
        
        // Assign free subscription to the company
        await this.assignFreeSubscriptionToCompany(subscription.companyId);
        
        console.log(`Company ${subscription.companyId} reverted to Free plan`);
        
        // Store event in Redis for audit
        await this.redisClient.hSet(
          `subscription_expiry:${subscription.companyId}`,
          new Date().toISOString(),
          'Reverted to Free plan'
        );
      }
    } catch (error) {
      console.error('Error checking expired subscriptions:', error);
    }
  }

  async getAllCompanySubscriptions(companyId: string) {
    try {
      const subscriptions = await this.companySubscriptionRepository.find({
        where: { companyId: companyId },
        relations: ['subscription'],
        order: { created_at: 'DESC' }
      });
      
      return subscriptions;
    } catch (error) {
      console.error('Error getting all company subscriptions:', error);
      return [];
    }
  }

  async getSubscriptionByName(name: string): Promise<Subscription> {
    return this.subscriptionRepository.findOne({
      where: { name }
    });
  }

  async createCompanySubscription(data: any): Promise<CompanySubscription> {
    const companySubscription = this.companySubscriptionRepository.create(data);
    // Use save and explicitly cast the result to CompanySubscription
    const savedSubscription = await this.companySubscriptionRepository.save(companySubscription);
    return Array.isArray(savedSubscription) ? savedSubscription[0] : savedSubscription;
  }

  async updateCompanySubscription(id: string, data: any): Promise<CompanySubscription> {
    await this.companySubscriptionRepository.update(id, data);
    const updatedSubscription = await this.companySubscriptionRepository.findOne({
      where: { id },
      relations: ['subscription']
    });
    
    if (!updatedSubscription) {
      throw new NotFoundException(`Company subscription with ID ${id} not found`);
    }
    
    return updatedSubscription;
  }

  async setActiveSubscription(companyId: string, subscriptionId: string): Promise<void> {
    // Get the subscription
    const subscription = await this.companySubscriptionRepository.findOne({
      where: { id: subscriptionId },
      relations: ['subscription']
    });
    
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }
    
    // Make sure this subscription is properly set up
    if (subscription.subscription.name === 'Enterprise') {
      // Ensure the end_date is properly set
      if (!subscription.end_date) {
        const endDate = new Date();
        endDate.setFullYear(endDate.getFullYear() + 1); // Default to 1 year
        subscription.end_date = endDate;
      }
      
      // Ensure tokens are properly set
      if (subscription.remaining_tokens <= 0) {
        subscription.remaining_tokens = subscription.subscription.token_limit;
      }
      
      // Save the updated subscription
      await this.companySubscriptionRepository.save(subscription);
      
      console.log(`Set subscription ${subscriptionId} as active for company ${companyId}`);
    }
  }
}
