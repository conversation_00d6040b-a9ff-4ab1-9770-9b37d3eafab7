# ----------- Build Stage -----------
FROM node:22-alpine AS builder

WORKDIR /app

# Install dependencies (including devDependencies for build)
COPY package*.json ./
RUN npm install

COPY . .

RUN npm run build

# Copy template files (as per postbuild script)
RUN npm run postbuild

# ----------- Production Stage -----------
FROM node:22-alpine AS production

WORKDIR /app

# Install only production dependencies
COPY package*.json ./
RUN npm install --omit=dev

# Copy built files and templates from builder
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules

# Declare the build argument
ARG DB_HOST
ARG DB_PORT
ARG DB_USERNAME
ARG DB_PASSWORD
ARG DB_DATABASE
ARG REDIS_URL
ARG REDIS_PASSWORD
ARG REDIS_DB
ARG JWT_SECRET
ARG NODE_ENV
ARG GITHUB_CLIENT_ID
ARG GITHUB_CLIENT_SECRET
ARG GITHUB_CALLBACK_URL
ARG GOOGLE_CLIENT_ID
ARG GOOGLE_CLIENT_SECRET
ARG GOOGLE_CALLBACK_URL
ARG FRONTEND_URL
ARG AGENTQ_APP_URL
ARG AGENTQ_APP_API_URL
ARG RESEND_API_KEY
ARG RESEND_FROM_EMAIL
ARG RESEND_FROM_NAME
ARG MIDTRANS_CLIENT_KEY
ARG MIDTRANS_SERVER_KEY

# Set it as an environment variable so npm run build can pick it up
ENV DB_HOST=${DB_HOST}
ENV DB_PORT=${DB_PORT}
ENV DB_USERNAME=${DB_USERNAME}
ENV DB_PASSWORD=${DB_PASSWORD}
ENV DB_DATABASE=${DB_DATABASE}
ENV REDIS_URL=${REDIS_URL}
ENV REDIS_PASSWORD=${REDIS_PASSWORD}
ENV REDIS_DB=${REDIS_DB}
ENV JWT_SECRET=${JWT_SECRET}
ENV NODE_ENV=${NODE_ENV}
ENV GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
ENV GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
ENV GITHUB_CALLBACK_URL=${GITHUB_CALLBACK_URL}
ENV GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
ENV GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
ENV GOOGLE_CALLBACK_URL=${GOOGLE_CALLBACK_URL}
ENV FRONTEND_URL=${FRONTEND_URL}
ENV AGENTQ_APP_URL=${AGENTQ_APP_URL}
ENV AGENTQ_APP_API_URL=${AGENTQ_APP_API_URL}
ENV RESEND_API_KEY=${RESEND_API_KEY}
ENV RESEND_FROM_EMAIL=${RESEND_FROM_EMAIL}
ENV RESEND_FROM_NAME=${RESEND_FROM_NAME}
ENV MIDTRANS_CLIENT_KEY=${MIDTRANS_CLIENT_KEY}
ENV MIDTRANS_SERVER_KEY=${MIDTRANS_SERVER_KEY}

# Expose the port the app runs on
EXPOSE 3000

# Healthcheck (optional, adjust endpoint as needed)
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/health || exit 1

# Use node user for security (optional, can be commented if issues)
USER node

# Start the application
CMD ["node", "dist/main"]
