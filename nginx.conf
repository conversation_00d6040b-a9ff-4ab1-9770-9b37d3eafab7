server {
  listen 80;
  server_name _;

  root /usr/share/nginx/html;
  index index.html;
  add_header Access-Control-Allow-Origin *;

  # Serve static assets locally
    location /assets/ {
        gzip_static on;
        expires max;
        add_header Cache-Control public;
    }

  location ~ ^/(dashboard|login|signup|auth/callback|confirm-email|forgot-password|reset-password|confirm-email-member-invitation|payment/finish|payment/resume|invoice|settings) {
        try_files $uri $uri/ /index.html;
    }

  # Optional: gzip static assets for better performance
  gzip on;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
  gzip_min_length 256;
}
