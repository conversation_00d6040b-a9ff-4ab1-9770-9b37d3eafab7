#!/bin/bash

# Load environment variables from .env.staging file
if [ -f .env.staging ]; then
  export $(grep -v '^#' .env.staging | xargs)
fi

# Now build your Docker image, passing the loaded environment variables as build-args
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t asia-southeast2-docker.pkg.dev/agentq-464900/agentq/backend_agentq:1.0.0 \
  --build-arg DB_HOST="${DB_HOST}" \
  --build-arg DB_PORT="${DB_PORT}" \
  --build-arg DB_USER="${DB_USER}" \
  --build-arg DB_PASSWORD="${DB_PASSWORD}" \
  --build-arg DB_NAME="${DB_NAME}" \
  --build-arg REDIS_HOST="${REDIS_HOST}" \
  --build-arg REDIS_PORT="${REDIS_PORT}" \
  --build-arg REDIS_PASSWORD="${REDIS_PASSWORD}" \
  --build-arg REDIS_DB="${REDIS_DB}" \
  --build-arg JWT_SECRET="${JWT_SECRET}" \
  --build-arg NODE_ENV="${NODE_ENV}" \
  --build-arg GITHUB_CLIENT_ID="${GITHUB_CLIENT_ID}" \
  --build-arg GITHUB_CLIENT_SECRET="${GITHUB_CLIENT_SECRET}" \
  --build-arg GITHUB_CALLBACK_URL="${GITHUB_CALLBACK_URL}" \
  --build-arg GOOGLE_CLIENT_ID="${GOOGLE_CLIENT_ID}" \
  --build-arg GOOGLE_CLIENT_SECRET="${GOOGLE_CLIENT_SECRET}" \
  --build-arg GOOGLE_CALLBACK_URL="${GOOGLE_CALLBACK_URL}" \
  --build-arg FRONTEND_URL="${FRONTEND_URL}" \
  --build-arg AGENTQ_APP_URL="${AGENTQ_APP_URL}" \
  --build-arg AGENTQ_APP_API_URL="${AGENTQ_APP_API_URL}" \
  --build-arg RESEND_API_KEY="${RESEND_API_KEY}" \
  --build-arg RESEND_FROM_EMAIL="${RESEND_FROM_EMAIL}" \
  --build-arg RESEND_FROM_NAME="${RESEND_FROM_NAME}" \
  --build-arg MIDTRANS_CLIENT_KEY="${MIDTRANS_CLIENT_KEY}" \
  --build-arg MIDTRANS_SERVER_KEY="${MIDTRANS_SERVER_KEY}" \
  --push .
