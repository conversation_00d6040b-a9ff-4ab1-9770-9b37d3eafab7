#!/bin/bash

# Load environment variables from .env.staging file
if [ -f .env.staging ]; then
  export $(grep -v '^#' .env.staging | xargs)
fi

# Now build your Docker image, passing the loaded environment variables as build-args
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t asia-southeast2-docker.pkg.dev/agentq-464900/agentq/frontend_agentq:1.0.0 \
  --build-arg VITE_CORE_SERVICE_URL="${VITE_CORE_SERVICE_URL}" \
  --build-arg VITE_APP_URL="${VITE_APP_URL}" \
  --push .



# docker buildx build \
#   --platform linux/amd64,linux/arm64 \
#   --build-arg VITE_CORE_SERVICE_URL="http://localhost:3000" \
#   --build-arg VITE_APP_URL="http://localhost:5174" \
#   -t frontend_agentq:latest \
#   .
